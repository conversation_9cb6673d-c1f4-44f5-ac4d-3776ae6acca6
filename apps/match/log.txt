 回放数据: {
  "id": "test_1759106069795_5gcc4fe0w",
  "type": "response",
  "service": "gateway",
  "action": "response",
  "payload": {
    "success": true,
    "data": {
      "battleRecord": {
        "battleRoundInfo": [
          {
            "eventTime": 1050,
            "moraleA": 300,
            "moraleB": 381,
            "attackerType": "teamB",
            "attackMode": 1,
            "scoreA": 0,
            "scoreB": 0,
            "periodInfo": [
              {
                "A1Info": {
                  "heroId": "enemy_90101_90010",
                  "attrType1": 1,
                  "attrValue1": 0,
                  "addValue1": 0,
                  "attrType2": 2,
                  "attrValue2": 0,
                  "addValue2": 0
                },
                "actionId": 0,
                "startCommentId": 104002,
                "resultCommentId": 0,
                "actionPer": 1000,
                "actionResult": 1,
                "moraleA": 381,
                "moraleB": 300,
                "skillEffectList": []
              },
              {
                "A1Info": {
                  "heroId": "enemy_90101_90010",
                  "attrType1": 1,
                  "attrValue1": 0,
                  "addValue1": 0,
                  "attrType2": 2,
                  "attrValue2": 0,
                  "addValue2": 0
                },
                "actionId": 0,
                "startCommentId": 103001,
                "resultCommentId": 0,
                "actionPer": 581,
                "actionResult": 0,
                "moraleA": 380,
                "moraleB": 302,
                "skillEffectList": []
              }
            ],
            "moraleSlotA": 200000,
            "moraleSlotB": 157500
          },
          {
            "eventTime": 1331,
            "moraleA": 302,
            "moraleB": 380,
            "attackerType": "teamA",
            "attackMode": 3,
            "scoreA": 0,
            "scoreB": 0,
            "periodInfo": [
              {
                "A1Info": {
                  "heroId": "hero_1759106067997_ktpjwj3cn",
                  "attrType1": 1,
                  "attrValue1": 37,
                  "addValue1": 0,
                  "attrType2": 2,
                  "attrValue2": 39,
                  "addValue2": 0
                },
                "actionId": 0,
                "startCommentId": 101100,
                "resultCommentId": 0,
                "actionPer": 1000,
                "actionResult": 1,
                "moraleA": 302,
                "moraleB": 380,
                "skillEffectList": []
              },
              {
                "A1Info": {
                  "heroId": "hero_1759106067997_ktpjwj3cn",
                  "attrType1": 1,
                  "attrValue1": 37,
                  "addValue1": 0,
                  "attrType2": 2,
                  "attrValue2": 39,
                  "addValue2": 0
                },
                "BInfo": {
                  "heroId": "enemy_90101_90001",
                  "attrType1": 4,
                  "attrValue1": 0,
                  "addValue1": 0,
                  "attrType2": 6,
                  "attrValue2": 0,
                  "addValue2": 0
                },
                "actionId": 0,
                "startCommentId": 0,
                "resultCommentId": 108011,
                "actionPer": 422,
                "actionResult": 0,
                "moraleA": 301,
                "moraleB": 382,
                "skillEffectList": []
              }
            ],
            "moraleSlotA": 200000,
            "moraleSlotB": 53390
          },
          {
            "eventTime": 2099,
            "moraleA": 301,
            "moraleB": 382,
            "attackerType": "teamB",
            "attackMode": 1,
            "scoreA": 0,
            "scoreB": 0,
            "periodInfo": [
              {
                "A1Info": {
                  "heroId": "enemy_90101_90009",
                  "attrType1": 1,
                  "attrValue1": 0,
                  "addValue1": 0,
                  "attrType2": 2,
                  "attrValue2": 0,
                  "addValue2": 0
                },
                "actionId": 0,
                "startCommentId": 104001,
                "resultCommentId": 0,
                "actionPer": 1000,
                "actionResult": 1,
                "moraleA": 382,
                "moraleB": 301,
                "skillEffectList": []
              },
              {
                "A1Info": {
                  "heroId": "enemy_90101_90009",
                  "attrType1": 1,
                  "attrValue1": 0,
                  "addValue1": 0,
                  "attrType2": 2,
                  "attrValue2": 0,
                  "addValue2": 0
                },
                "actionId": 0,
                "startCommentId": 0,
                "resultCommentId": 102031,
                "actionPer": 581,
                "actionResult": 1,
                "moraleA": 382,
                "moraleB": 301,
                "skillEffectList": []
              },
              {
                "A1Info": {
                  "heroId": "enemy_90101_90009",
                  "attrType1": 1,
                  "attrValue1": 0,
                  "addValue1": 0,
                  "attrType2": 2,
                  "attrValue2": 0,
                  "addValue2": 0
                },
                "BInfo": {
                  "heroId": "hero_1759106067989_ns5jgbs4s",
                  "attrType1": 4,
                  "attrValue1": 42,
                  "addValue1": 0,
                  "attrType2": 6,
                  "attrValue2": 44,
                  "addValue2": 0
                },
                "GKInfo": {
                  "heroId": "hero_1759106067987_440ef2cmc",
                  "attrType1": 7,
                  "attrValue1": 41,
                  "addValue1": 0,
                  "attrType2": 6,
                  "attrValue2": 41,
                  "addValue2": 0
                },
                "actionId": 0,
                "startCommentId": 304001,
                "resultCommentId": 304020,
                "actionPer": 600,
                "actionResult": 0,
                "moraleA": 377,
                "moraleB": 301,
                "skillEffectList": []
              }
            ],
            "moraleSlotA": 200000,
            "moraleSlotB": 115584
          },
          {
            "eventTime": 2660,
            "moraleA": 301,
            "moraleB": 377,
            "attackerType": "teamA",
            "attackMode": 3,
            "scoreA": 0,
            "scoreB": 0,
            "periodInfo": [
              {
                "A1Info": {
                  "heroId": "hero_1759106067999_veojbbbmc",
                  "attrType1": 1,
                  "attrValue1": 39,
                  "addValue1": 0,
                  "attrType2": 2,
                  "attrValue2": 37,
                  "addValue2": 0
                },
                "actionId": 0,
                "startCommentId": 101100,
                "resultCommentId": 0,
                "actionPer": 1000,
                "actionResult": 1,
                "moraleA": 301,
                "moraleB": 377,
                "skillEffectList": []
              },
              {
                "A1Info": {
                  "heroId": "hero_1759106067999_veojbbbmc",
                  "attrType1": 1,
                  "attrValue1": 39,
                  "addValue1": 0,
                  "attrType2": 2,
                  "attrValue2": 37,
                  "addValue2": 0
                },
                "BInfo": {
                  "heroId": "enemy_90101_90004",
                  "attrType1": 4,
                  "attrValue1": 0,
                  "addValue1": 0,
                  "attrType2": 6,
                  "attrValue2": 0,
                  "addValue2": 0
                },
                "actionId": 0,
                "startCommentId": 0,
                "resultCommentId": 108000,
                "actionPer": 424,
                "actionResult": 1,
                "moraleA": 301,
                "moraleB": 377,
                "skillEffectList": []
              },
              {
                "A2Info": {
                  "heroId": "hero_1759106067987_440ef2cmc",
                  "attrType1": 3,
                  "attrValue1": 43,
                  "addValue1": 0,
                  "attrType2": 5,
                  "attrValue2": 41,
                  "addValue2": 0
                },
                "BInfo": {
                  "heroId": "enemy_90101_90001",
                  "attrType1": 4,
                  "attrValue1": 0,
                  "addValue1": 0,
                  "attrType2": 6,
                  "attrValue2": 0,
                  "addValue2": 0
                },
                "GKInfo": {
                  "heroId": "enemy_90101_90003",
                  "attrType1": 7,
                  "attrValue1": 0,
                  "addValue1": 0,
                  "attrType2": 6,
                  "attrValue2": 0,
                  "addValue2": 0
                },
                "actionId": 0,
                "startCommentId": 301000,
                "resultCommentId": 301020,
                "actionPer": 197,
                "actionResult": 0,
                "moraleA": 296,
                "moraleB": 377,
                "skillEffectList": []
              }
            ],
            "moraleSlotA": 200000,
            "moraleSlotB": 105749
          },
          {
            "eventTime": 3160,
            "moraleA": 296,
            "moraleB": 377,
            "attackerType": "teamB",
            "attackMode": 4,
            "scoreA": 0,
            "scoreB": 0,
            "periodInfo": [
              {
                "A1Info": {
                  "heroId": "enemy_90101_90010",
                  "attrType1": 1,
                  "attrValue1": 0,
                  "addValue1": 0,
                  "attrType2": 2,
                  "attrValue2": 0,
                  "addValue2": 0
                },
                "actionId": 0,
                "startCommentId": 0,
                "resultCommentId": 0,
                "actionPer": 1000,
                "actionResult": 1,
                "moraleA": 377,
                "moraleB": 296,
                "skillEffectList": []
              },
              {
                "A1Info": {
                  "heroId": "enemy_90101_90010",
                  "attrType1": 1,
                  "attrValue1": 0,
                  "addValue1": 0,
                  "attrType2": 2,
                  "attrValue2": 0,
                  "addValue2": 0
                },
                "BInfo": {
                  "heroId": "hero_1759106067993_vyvrulqfj",
                  "attrType1": 4,
                  "attrValue1": 38,
                  "addValue1": 0,
                  "attrType2": 6,
                  "attrValue2": 45,
                  "addValue2": 0
                },
                "actionId": 0,
                "startCommentId": 0,
                "resultCommentId": 103050,
                "actionPer": 581,
                "actionResult": 1,
                "moraleA": 377,
                "moraleB": 296,
                "skillEffectList": []
              },
              {
                "BInfo": {
                  "heroId": "hero_1759106067989_ns5jgbs4s",
                  "attrType1": 4,
                  "attrValue1": 42,
                  "addValue1": 0,
                  "attrType2": 6,
                  "attrValue2": 44,
                  "addValue2": 0
                },
                "GKInfo": {
                  "heroId": "hero_1759106067991_e0b78ergz",
                  "attrType1": 7,
                  "attrValue1": 39,
                  "addValue1": 0,
                  "attrType2": 6,
                  "attrValue2": 44,
                  "addValue2": 0
                },
                "actionId": 0,
                "startCommentId": 303000,
                "resultCommentId": 303010,
                "actionPer": 600,
                "actionResult": 1,
                "moraleA": 377,
                "moraleB": 296,
                "skillEffectList": []
              }
            ],
            "moraleSlotA": 200000,
            "moraleSlotB": 74000
          },
          {
            "eventTime": 4011,
            "moraleA": 296,
            "moraleB": 377,
            "attackerType": "teamA",
            "attackMode": 4,
            "scoreA": 0,
            "scoreB": 1,
            "periodInfo": [
              {
                "A1Info": {
                  "heroId": "hero_1759106067997_ktpjwj3cn",
                  "attrType1": 1,
                  "attrValue1": 37,
                  "addValue1": 0,
                  "attrType2": 2,
                  "attrValue2": 39,
                  "addValue2": 0
                },
                "actionId": 0,
                "startCommentId": 0,
                "resultCommentId": 0,
                "actionPer": 1000,
                "actionResult": 1,
                "moraleA": 296,
                "moraleB": 377,
                "skillEffectList": []
              },
              {
                "A1Info": {
                  "heroId": "hero_1759106067997_ktpjwj3cn",
                  "attrType1": 1,
                  "attrValue1": 37,
                  "addValue1": 0,
                  "attrType2": 2,
                  "attrValue2": 39,
                  "addValue2": 0
                },
                "BInfo": {
                  "heroId": "enemy_90101_90002",
                  "attrType1": 4,
                  "attrValue1": 0,
                  "addValue1": 0,
                  "attrType2": 6,
                  "attrValue2": 0,
                  "addValue2": 0
                },
                "actionId": 0,
                "startCommentId": 103000,
                "resultCommentId": 103020,
                "actionPer": 419,
                "actionResult": 0,
                "moraleA": 295,
                "moraleB": 379,
                "skillEffectList": [
                  {
                    "skillId": 11035,
                    "teamType": "teamA",
                    "addPer": 3,
                    "heroId": "hero_1759106067997_ktpjwj3cn"
                  }
                ]
              }
            ],
            "moraleSlotA": 200000,
            "moraleSlotB": 160414
          },
          {
            "eventTime": 4220,
            "moraleA": 295,
            "moraleB": 379,
            "attackerType": "teamB",
            "attackMode": 1,
            "scoreA": 0,
            "scoreB": 1,
            "periodInfo": [
              {
                "A1Info": {
                  "heroId": "enemy_90101_90010",
                  "attrType1": 1,
                  "attrValue1": 0,
                  "addValue1": 0,
                  "attrType2": 2,
                  "attrValue2": 0,
                  "addValue2": 0
                },
                "actionId": 0,
                "startCommentId": 104000,
                "resultCommentId": 0,
                "actionPer": 1000,
                "actionResult": 1,
                "moraleA": 379,
                "moraleB": 295,
                "skillEffectList": []
              },
              {
                "A1Info": {
                  "heroId": "enemy_90101_90010",
                  "attrType1": 1,
                  "attrValue1": 0,
                  "addValue1": 0,
                  "attrType2": 2,
                  "attrValue2": 0,
                  "addValue2": 0
                },
                "actionId": 0,
                "startCommentId": 103000,
                "resultCommentId": 103011,
                "actionPer": 584,
                "actionResult": 1,
                "moraleA": 379,
                "moraleB": 295,
                "skillEffectList": []
              },
              {
                "A1Info": {
                  "heroId": "enemy_90101_90010",
                  "attrType1": 1,
                  "attrValue1": 0,
                  "addValue1": 0,
                  "attrType2": 2,
                  "attrValue2": 0,
                  "addValue2": 0
                },
                "BInfo": {
                  "heroId": "hero_1759106067989_ns5jgbs4s",
                  "attrType1": 4,
                  "attrValue1": 42,
                  "addValue1": 0,
                  "attrType2": 6,
                  "attrValue2": 44,
                  "addValue2": 0
                },
                "GKInfo": {
                  "heroId": "hero_1759106067991_e0b78ergz",
                  "attrType1": 7,
                  "attrValue1": 39,
                  "addValue1": 0,
                  "attrType2": 6,
                  "attrValue2": 44,
                  "addValue2": 0
                },
                "actionId": 0,
                "startCommentId": 304001,
                "resultCommentId": 304011,
                "actionPer": 600,
                "actionResult": 1,
                "moraleA": 379,
                "moraleB": 295,
                "skillEffectList": []
              }
            ],
            "moraleSlotA": 200000,
            "moraleSlotB": 30828
          },
          {
            "eventTime": 5275,
            "moraleA": 295,
            "moraleB": 379,
            "attackerType": "teamB",
            "attackMode": 4,
            "scoreA": 0,
            "scoreB": 2,
            "periodInfo": [
              {
                "A1Info": {
                  "heroId": "enemy_90101_90010",
                  "attrType1": 1,
                  "attrValue1": 0,
                  "addValue1": 0,
                  "attrType2": 2,
                  "attrValue2": 0,
                  "addValue2": 0
                },
                "actionId": 0,
                "startCommentId": 0,
                "resultCommentId": 0,
                "actionPer": 1000,
                "actionResult": 1,
                "moraleA": 379,
                "moraleB": 295,
                "skillEffectList": []
              },
              {
                "A1Info": {
                  "heroId": "enemy_90101_90010",
                  "attrType1": 1,
                  "attrValue1": 0,
                  "addValue1": 0,
                  "attrType2": 2,
                  "attrValue2": 0,
                  "addValue2": 0
                },
                "BInfo": {
                  "heroId": "hero_1759106067987_440ef2cmc",
                  "attrType1": 4,
                  "attrValue1": 36,
                  "addValue1": 0,
                  "attrType2": 6,
                  "attrValue2": 41,
                  "addValue2": 0
                },
                "actionId": 0,
                "startCommentId": 0,
                "resultCommentId": 103060,
                "actionPer": 584,
                "actionResult": 0,
                "moraleA": 378,
                "moraleB": 297,
                "skillEffectList": []
              }
            ],
            "moraleSlotA": 200000,
            "moraleSlotB": 186441
          },
          {
            "eventTime": 5366,
            "moraleA": 297,
            "moraleB": 378,
            "attackerType": "teamA",
            "attackMode": 3,
            "scoreA": 0,
            "scoreB": 2,
            "periodInfo": [
              {
                "A1Info": {
                  "heroId": "hero_1759106067999_veojbbbmc",
                  "attrType1": 1,
                  "attrValue1": 39,
                  "addValue1": 0,
                  "attrType2": 2,
                  "attrValue2": 37,
                  "addValue2": 0
                },
                "actionId": 0,
                "startCommentId": 101010,
                "resultCommentId": 0,
                "actionPer": 1000,
                "actionResult": 1,
                "moraleA": 297,
                "moraleB": 378,
                "skillEffectList": []
              },
              {
                "A1Info": {
                  "heroId": "hero_1759106067999_veojbbbmc",
                  "attrType1": 1,
                  "attrValue1": 39,
                  "addValue1": 0,
                  "attrType2": 2,
                  "attrValue2": 37,
                  "addValue2": 0
                },
                "BInfo": {
                  "heroId": "enemy_90101_90004",
                  "attrType1": 4,
                  "attrValue1": 0,
                  "addValue1": 0,
                  "attrType2": 6,
                  "attrValue2": 0,
                  "addValue2": 0
                },
                "actionId": 0,
                "startCommentId": 0,
                "resultCommentId": 108000,
                "actionPer": 419,
                "actionResult": 1,
                "moraleA": 297,
                "moraleB": 378,
                "skillEffectList": []
              },
              {
                "A2Info": {
                  "heroId": "hero_1759106067987_440ef2cmc",
                  "attrType1": 3,
                  "attrValue1": 43,
                  "addValue1": 0,
                  "attrType2": 5,
                  "attrValue2": 41,
                  "addValue2": 0
                },
                "BInfo": {
                  "heroId": "enemy_90101_90001",
                  "attrType1": 4,
                  "attrValue1": 0,
                  "addValue1": 0,
                  "attrType2": 6,
                  "attrValue2": 0,
                  "addValue2": 0
                },
                "GKInfo": {
                  "heroId": "enemy_90101_90004",
                  "attrType1": 7,
                  "attrValue1": 0,
                  "addValue1": 0,
                  "attrType2": 6,
                  "attrValue2": 0,
                  "addValue2": 0
                },
                "actionId": 0,
                "startCommentId": 301000,
                "resultCommentId": 301020,
                "actionPer": 195,
                "actionResult": 0,
                "moraleA": 292,
                "moraleB": 378,
                "skillEffectList": []
              }
            ],
            "moraleSlotA": 200000,
            "moraleSlotB": 17199
          }
        ],
        "totalTime": 6333,
        "totalRounds": 10
      },
      "teamAData": {
        "characterId": "char_server_001_04bd2031_pa9cq172e",
        "teamType": "teamA",
        "teamName": "队伍char_server_001_04bd2031_pa9cq172e",
        "rating": 0,
        "formationId": 442101,
        "attackTacticId": 101,
        "defendTacticId": 101,
        "heroes": [
          {
            "heroId": "hero_1759106067985_x94loqb9k",
            "name": "巴塔查亚",
            "position": "GK",
            "rating": 1
          },
          {
            "heroId": "hero_1759106067987_440ef2cmc",
            "name": "弗赖马尼斯",
            "position": "DC",
            "rating": 1
          },
          {
            "heroId": "hero_1759106067989_ns5jgbs4s",
            "name": "加文.霍伊特",
            "position": "DL",
            "rating": 1
          },
          {
            "heroId": "hero_1759106067991_e0b78ergz",
            "name": "贾斯汀努森",
            "position": "DR",
            "rating": 1
          },
          {
            "heroId": "hero_1759106067993_vyvrulqfj",
            "name": "哈布拉",
            "position": "MC",
            "rating": 1
          },
          {
            "heroId": "hero_1759106067994_9odqmrjix",
            "name": "马塞兰",
            "position": "ML",
            "rating": 1
          },
          {
            "heroId": "hero_1759106067995_oqzljfwbr",
            "name": "斯图波尔",
            "position": "MR",
            "rating": 1
          },
          {
            "heroId": "hero_1759106067997_ktpjwj3cn",
            "name": "佩斯科",
            "position": "WL",
            "rating": 1
          },
          {
            "heroId": "hero_1759106067999_veojbbbmc",
            "name": "拉兹丁什",
            "position": "WR",
            "rating": 1
          },
          {
            "heroId": "hero_1759106068000_s59dydx3g",
            "name": "雷加莱斯",
            "position": "ST",
            "rating": 1
          },
          {
            "heroId": "hero_1759106068001_mmg8y74ix",
            "name": "莫里斯",
            "position": "AM",
            "rating": 1
          }
        ],
        "attackTacticsState": 0,
        "defendTacticsState": 0,
        "baseAttackValue": 0,
        "baseDefendValue": 0,
        "attackValueFactor": 1,
        "defendValueFactor": 1
      },
      "teamBData": {
        "characterId": "AI",
        "teamType": "teamB",
        "teamName": "红星",
        "rating": 200,
        "formationId": 442201,
        "attackTacticId": 101,
        "defendTacticId": 101,
        "heroes": [
          {
            "heroId": "enemy_90101_90000",
            "name": "未知球员",
            "position": "GK",
            "rating": 1
          },
          {
            "heroId": "enemy_90101_90001",
            "name": "未知球员",
            "position": "DC",
            "rating": 1
          },
          {
            "heroId": "enemy_90101_90002",
            "name": "未知球员",
            "position": "DC",
            "rating": 1
          },
          {
            "heroId": "enemy_90101_90003",
            "name": "未知球员",
            "position": "DL",
            "rating": 1
          },
          {
            "heroId": "enemy_90101_90004",
            "name": "未知球员",
            "position": "DR",
            "rating": 1
          },
          {
            "heroId": "enemy_90101_90005",
            "name": "未知球员",
            "position": "DM",
            "rating": 1
          },
          {
            "heroId": "enemy_90101_90006",
            "name": "未知球员",
            "position": "MC",
            "rating": 1
          },
          {
            "heroId": "enemy_90101_90007",
            "name": "未知球员",
            "position": "MC",
            "rating": 1
          },
          {
            "heroId": "enemy_90101_90008",
            "name": "未知球员",
            "position": "AM",
            "rating": 1
          },
          {
            "heroId": "enemy_90101_90009",
            "name": "未知球员",
            "position": "ST",
            "rating": 1
          },
          {
            "heroId": "enemy_90101_90010",
            "name": "未知球员",
            "position": "ST",
            "rating": 1
          }
        ],
        "attackTacticsState": 0,
        "defendTacticsState": 0,
        "baseAttackValue": 0,
        "baseDefendValue": 0,
        "attackValueFactor": 1,
        "defendValueFactor": 1
      },
      "battleResult": {
        "roomId": "room_1759106069419_7815",
        "battleType": "PVE_LEAGUE",
        "homeScore": 0,
        "awayScore": 2,
        "winner": 2,
        "totalTime": 6333,
        "totalRounds": 10,
        "teamAStats": {
          "shotNum": 0,
          "ctrlBallPer": 44,
          "breakPer": 50,
          "bestBaller": "hero_1759106067999_veojbbbmc",
          "heroRatings": {
            "hero_1759106067985_x94loqb9k": 50,
            "hero_1759106067987_440ef2cmc": 53,
            "hero_1759106067989_ns5jgbs4s": 50,
            "hero_1759106067991_e0b78ergz": 48,
            "hero_1759106067993_vyvrulqfj": 50,
            "hero_1759106067994_9odqmrjix": 50,
            "hero_1759106067995_oqzljfwbr": 50,
            "hero_1759106067997_ktpjwj3cn": 54,
            "hero_1759106067999_veojbbbmc": 72,
            "hero_1759106068000_s59dydx3g": 50,
            "hero_1759106068001_mmg8y74ix": 50
          }
        },
        "teamBStats": {
          "shotNum": 2,
          "ctrlBallPer": 56,
          "breakPer": 60,
          "bestBaller": "enemy_90101_90010",
          "heroRatings": {
            "enemy_90101_90000": 50,
            "enemy_90101_90001": 50,
            "enemy_90101_90002": 50,
            "enemy_90101_90003": 53,
            "enemy_90101_90004": 53,
            "enemy_90101_90005": 50,
            "enemy_90101_90006": 50,
            "enemy_90101_90007": 50,
            "enemy_90101_90008": 50,
            "enemy_90101_90009": 61,
            "enemy_90101_90010": 94
          }
        },
        "goals": [
          {
            "time": 1759106069638,
            "teamType": "teamB",
            "ballerHeroId": "enemy_90101_90010",
            "assistHeroId": ""
          },
          {
            "time": 1759106069677,
            "teamType": "teamB",
            "ballerHeroId": "enemy_90101_90010",
            "assistHeroId": ""
          }
        ],
        "rewards": null,
        "lootItems": [],
        "skillRecords": [
          {
            "durRecord": [],
            "insRecord": [
              {
                "skillId": 11035,
                "round": 5,
                "heroId": "hero_1759106067997_ktpjwj3cn",
                "period": 1
              }
            ],
            "nextAtkRecord": []
          },
          {
            "durRecord": [],
            "insRecord": [],
            "nextAtkRecord": []
          }
        ],
        "commentSummary": {
          "matchSummary": "⚽ 比赛结束！队伍char_server_001_04bd2031_pa9cq172e 0 : 2 红星\n⏱️ 比赛用时：105分钟\n🏆 红星获得胜利！\n📊 本场比赛共进行了10个回合\n📈 射门统计：队伍char_server_001_04bd2031_pa9cq172e 0 - 2 红星\n🎯 射正统计：队伍char_server_001_04bd2031_pa9cq172e 0 - 2 红星",
          "totalComments": 27,
          "goalComments": 4,
          "keyMoments": [
            "第52分钟进球",
            "第70分钟进球"
          ]
        }
      }
    },
    "code": "SUCCESS",
    "timestamp": 1759106069840,
    "requestId": "test_1759106069795_5gcc4fe0w",
    "duration": 46,
    "service": "match",
    "action": "battle.getBattleReplay"
  },
  "timestamp": 1759106069848
}