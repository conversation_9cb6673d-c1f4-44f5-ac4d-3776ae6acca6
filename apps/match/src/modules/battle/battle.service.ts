import { Injectable, Logger } from '@nestjs/common';
import { BattleRepository } from '../../common/repositories/battle.repository';
import { GameConfigFacade } from '@libs/game-config';
import { MicroserviceClientService } from '@libs/service-mesh';
import { MICROSERVICE_NAMES } from '@libs/shared/constants';
import {
  PveBattleDto,
  PvpBattleDto,
  BattleResultResponseDto,
  GetBattleReplayDto,
  GetBattleReplayResponseDto, BattleTeamDataDto
} from '../../common/dto/battle.dto';
import { BattleRoom, RoomStatus, TeamType } from '../../common/schemas/battle.schema';
import {BattleResult, BattleTeam} from './types/battle-data.types';
import { BattleEngine } from './battle-engine';

// Result模式相关导入
import { BaseService } from '@libs/common/service';
import { XResult, XResultUtils } from '@libs/common/types/result.type';

/**
 * 战斗系统服务
 * 严格基于old项目battleService.js的业务逻辑实现
 *
 * 核心功能：
 * - PVE战斗计算
 * - PVP战斗计算
 * - 战斗房间管理
 * - 战斗回放生成
 * - 战斗结果处理
 */
@Injectable()
export class BattleService extends BaseService {
  private readonly battleEngine: BattleEngine;

  constructor(
    private readonly battleRepository: BattleRepository,
    private readonly gameConfig: GameConfigFacade,
    microserviceClient: MicroserviceClientService, // 传递给BaseService
  ) {
    // 调用BaseService构造函数，传入服务名称和微服务客户端
    super('BattleService', microserviceClient);
    this.battleEngine = new BattleEngine(this.gameConfig);
  }

  /**
   * PVE战斗
   * 基于old项目: BattleService.prototype.initPveBattle
   */
  async pveBattle(dto: PveBattleDto): Promise<XResult<BattleResultResponseDto>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`PVE战斗开始: ${dto.characterId}, 类型: ${dto.battleType}`);

      // 1. 创建BattleRoom记录（仅记录战斗开始状态）
      const roomId = this.generateRoomId();
      const battleRoomRecord = await this.createBattleRoomRecord(roomId, dto);
      if (XResultUtils.isFailure(battleRoomRecord)) {
        return XResultUtils.error(`创建战斗记录失败: ${battleRoomRecord.message}`, battleRoomRecord.code);
      }

      // 2. 直接调用战斗计算（不经过BattleRoom转换）
      const battleResult = await this.battleEngine.executeBattleFromTeams(
        [dto.characterBattleData, dto.enemyBattleData],
        dto.battleType,
        roomId
      );

      // 调试日志：检查战斗结果数据结构
      this.logger.debug(`战斗结果数据结构检查:`, {
        hasBattleRecord: !!battleResult.battleRecord,
        hasStatistics: !!battleResult.statistics,
        battleRecordKeys: battleResult.battleRecord ? Object.keys(battleResult.battleRecord) : [],
        statisticsKeys: battleResult.statistics ? Object.keys(battleResult.statistics) : [],
        teamAStatsExists: !!battleResult.statistics?.teamA,
        teamBStatsExists: !!battleResult.statistics?.teamB,
        battleRoundInfoLength: battleResult.battleRecord?.battleRoundInfo?.length || 0,
        // 详细检查statistics内容
        statisticsDetail: battleResult.statistics ? JSON.stringify(battleResult.statistics, null, 2) : 'null',
        // 详细检查battleRecord内容
        battleRecordDetail: battleResult.battleRecord ? {
          battleRoundInfoLength: battleResult.battleRecord.battleRoundInfo?.length || 0,
          totalTime: battleResult.battleRecord.totalTime || 0,
          totalRounds: battleResult.battleRecord.totalRounds || 0
        } : 'null'
      });

      // 3. 更新BattleRoom记录（仅保存结果）
      const updateResult = await this.updateBattleRoomResult(roomId, battleResult);
      if (XResultUtils.isFailure(updateResult)) {
        this.logger.warn(`更新战斗记录失败: ${updateResult.message}`);
      }

      // 5. 延迟清理战斗房间（给回放查询留时间）
      this.scheduleRoomCleanup(roomId);

      const responseData: BattleResultResponseDto = {
        roomId: roomId,
        homeScore: battleResult.finalScore.teamA,
        awayScore: battleResult.finalScore.teamB,
        winner: this.determineWinner(battleResult.finalScore.teamA, battleResult.finalScore.teamB),
        battleRecord: battleResult.battleRecord,
        statistics: battleResult.statistics,
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'pve_battle',
      metadata: {
        characterId: dto.characterId,
        battleType: dto.battleType
      }
    });
  }

  /**
   * PVP战斗 - 重构版本
   * 消除错误的BattleRoom中转，直接进行战斗计算
   */
  async pvpBattle(dto: PvpBattleDto): Promise<XResult<BattleResultResponseDto>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`PVP战斗开始: ${dto.homeCharacterId} vs ${dto.awayCharacterId}`);

      // 1. 创建BattleRoom记录（仅记录战斗开始状态）
      const roomId = this.generateRoomId();
      const battleRoomRecord = await this.createPvpBattleRoomRecord(roomId, dto);
      if (XResultUtils.isFailure(battleRoomRecord)) {
        return XResultUtils.error(`创建战斗记录失败: ${battleRoomRecord.message}`, battleRoomRecord.code);
      }

      // 2. 直接调用战斗计算（不经过BattleRoom转换）
      const battleResult = await this.battleEngine.executeBattleFromTeams(
        [dto.homeBattleData, dto.awayBattleData],
        dto.battleType,
        roomId
      );

      // 3. 更新BattleRoom记录（仅保存结果）
      const updateResult = await this.updateBattleRoomResult(roomId, battleResult);
      if (XResultUtils.isFailure(updateResult)) {
        this.logger.warn(`更新战斗记录失败: ${updateResult.message}`);
      }

      const responseData: BattleResultResponseDto = {
        roomId: roomId,
        homeScore: battleResult.finalScore.teamA,
        awayScore: battleResult.finalScore.teamB,
        winner: this.determineWinner(battleResult.finalScore.teamA, battleResult.finalScore.teamB),
        battleRecord: battleResult.battleRecord,
        statistics: battleResult.statistics,
      };

      return XResultUtils.ok(responseData);
    }, {
      reason: 'pvp_battle',
      metadata: {
        homeCharacterId: dto.homeCharacterId,
        awayCharacterId: dto.awayCharacterId,
        battleType: dto.battleType
      }
    });
  }

  /**
   * 获取战斗回放
   * 基于old项目的战斗回放功能
   */
  async getBattleReplay(dto: GetBattleReplayDto): Promise<XResult<GetBattleReplayResponseDto>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`获取战斗回放: ${dto.roomId}`);

      const battleRoomResult = await this.battleRepository.findByRoomId(dto.roomId);
      this.logger.debug(`查询战斗房间结果: success=${XResultUtils.isSuccess(battleRoomResult)}, data=${!!battleRoomResult.data}`);

      if (XResultUtils.isFailure(battleRoomResult)) {
        this.logger.warn(`查询战斗房间失败: ${battleRoomResult.message}, code=${battleRoomResult.code}`);
        return XResultUtils.error(`查询战斗房间失败: ${battleRoomResult.message}`, battleRoomResult.code);
      }

      const battleRoom = battleRoomResult.data;
      if (!battleRoom) {
        this.logger.warn(`战斗房间不存在: ${dto.roomId}`);
        return XResultUtils.error('战斗房间不存在', 'BATTLE_ROOM_NOT_FOUND');
      }

      this.logger.debug(`战斗房间状态: ${battleRoom.status}, 是否有result: ${!!battleRoom.result}`);

      if (battleRoom.status !== 'finished') {
        this.logger.warn(`战斗尚未完成: status=${battleRoom.status}`);
        return XResultUtils.error('战斗尚未完成', 'BATTLE_NOT_FINISHED');
      }

      // 修复：检查result属性是否存在
      if (!battleRoom.result) {
        this.logger.warn(`战斗结果数据不存在: roomId=${dto.roomId}`);
        return XResultUtils.error('战斗结果数据不存在', 'BATTLE_RESULT_NOT_FOUND');
      }

      // 构建完整的战斗回放数据 - 符合优化后的schema结构
      const responseData: GetBattleReplayResponseDto = {
        battleRecord: {
          battleRoundInfo: battleRoom.result.rounds || [],
          totalTime: battleRoom.result.totalTime || 0,
          totalRounds: battleRoom.result.totalRounds || 0
        },
        teamAData: battleRoom.teamA,  // TeamSnapshoot - 替代了原preBattleInfo[0]
        teamBData: battleRoom.teamB,  // TeamSnapshoot - 替代了原preBattleInfo[1]
        // 战斗结果信息 - BattleResult中已经包含了原battleEndInfo的所有内容
        battleResult: {
          roomId: battleRoom.roomId,
          battleType: battleRoom.battleType,
          homeScore: battleRoom.result?.homeScore || 0,
          awayScore: battleRoom.result?.awayScore || 0,
          winner: battleRoom.result?.winner || 0,
          totalTime: battleRoom.result?.totalTime || 0,
          totalRounds: battleRoom.result?.totalRounds || 0,
          teamAStats: battleRoom.result?.teamAStats || {},
          teamBStats: battleRoom.result?.teamBStats || {},
          goals: battleRoom.result?.goals || [],
          rewards: battleRoom.result?.rewards || null,
          lootItems: battleRoom.result?.lootItems || [],
          skillRecords: battleRoom.result?.skillRecords || []
        }
      };

      this.logger.debug(`战斗回放数据: ${JSON.stringify(responseData, null, 2)}`);

      return XResultUtils.ok(responseData);
    }, {
      reason: 'get_battle_replay',
      metadata: { roomId: dto.roomId }
    });
  }

  /**
   * 删除战斗房间
   * 基于old项目: BattleService.prototype.deleteBattleRoom
   */
  async deleteBattleRoom(roomId: string): Promise<XResult<boolean>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`删除战斗房间: ${roomId}`);

      const deleteResult = await this.battleRepository.deleteByRoomId(roomId);
      if (XResultUtils.isFailure(deleteResult)) {
        return XResultUtils.error(`删除战斗房间失败: ${deleteResult.message}`, deleteResult.code);
      }

      return XResultUtils.ok(deleteResult.data);
    }, {
      reason: 'delete_battle_room',
      metadata: { roomId }
    });
  }

  // ==================== 私有方法 ====================

  /**
   * 生成房间ID
   * 基于old项目: utils.syncCreateUid()
   */
  private generateRoomId(): string {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 10000);
    return `room_${timestamp}_${random}`;
  }

  /**
   * 从玩家数据构建队伍数据
   */
  private buildTeamDataFromCharacterData(characterData: BattleTeam): any {
    // 添加详细日志来跟踪数据流
    this.logger.log(`=== buildTeamDataFromCharacterData 开始 ===`);
    this.logger.log(`输入characterData类型: ${typeof characterData}`);
    this.logger.log(`输入characterData.heroes类型: ${typeof characterData.heroes}`);
    this.logger.log(`输入characterData.heroes是否为数组: ${Array.isArray(characterData.heroes)}`);
    this.logger.log(`输入characterData.heroes长度: ${characterData.heroes?.length || 0}`);
    this.logger.log(`输入characterData完整数据: ${JSON.stringify(characterData, null, 2)}`);

    const teamData = {
      characterId: characterData.characterId || '',
      teamName: characterData.teamName || '',
      formationId: characterData.formationId || 0,
      tactic: characterData.tactic || 0,
      heroes: this.convertBattleHeroesToBattleHeroInfo(characterData.heroes || []),
      totalAttack: characterData.totalAttack || 0,
      totalDefend: characterData.totalDefend || 0,
      morale: characterData.attr?.morale || 0,
      score: 0,
    };

    this.logger.log(`输出teamData.heroes类型: ${typeof teamData.heroes}`);
    this.logger.log(`输出teamData.heroes是否为数组: ${Array.isArray(teamData.heroes)}`);
    this.logger.log(`输出teamData.heroes长度: ${teamData.heroes?.length || 0}`);
    this.logger.log(`=== buildTeamDataFromCharacterData 结束 ===`);

    return teamData;
  }

  /**
   * 从敌方配置构建队伍数据
   */
  private buildTeamDataFromEnemyData(enemyData: BattleTeam): any {
    return {
      characterId: 'ai_enemy',
      teamName: enemyData.teamName || 'AI队伍',
      formationId: enemyData.formationId || 0,
      tactic: enemyData.tactic || 0,
      heroes: this.convertBattleHeroesToBattleHeroInfo(enemyData.heroes || []),
      totalAttack: enemyData.totalAttack || 0,
      totalDefend: enemyData.totalDefend || 0,
      morale: enemyData.attr?.morale || 0,
      score: 0,
    };
  }

  /**
   * 判断胜负
   */
  private determineWinner(homeScore: number, awayScore: number): number {
    if (homeScore > awayScore) return 1;  // 主队胜
    if (awayScore > homeScore) return 2;  // 客队胜
    return 0;  // 平局
  }

  /**
   * 调度房间清理
   */
  private scheduleRoomCleanup(roomId: string): void {
    try {
      // PVE战斗房间保留30分钟用于回放查询，PVP战斗保留更长时间
      setTimeout(async () => {
        await this.cleanupBattleRoom(roomId);
      }, 30 * 60 * 1000); // 30分钟后清理
    } catch (error) {
      this.logger.error('调度房间清理失败', error);
    }
  }

  /**
   * 清理战斗房间
   */
  private async cleanupBattleRoom(roomId: string): Promise<XResult<void>> {
    const deleteResult = await this.battleRepository.deleteByRoomId(roomId);
    if (XResultUtils.isFailure(deleteResult)) {
      this.logger.error(`清理战斗房间异常: ${roomId}, ${deleteResult.message}`);
      return XResultUtils.error(`清理战斗房间失败: ${deleteResult.message}`, deleteResult.code);
    }

    if (deleteResult.data) {
      this.logger.log(`战斗房间已清理: ${roomId}`);
    } else {
      this.logger.warn(`战斗房间清理失败，房间不存在: ${roomId}`);
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 获取战斗统计信息
   */
  async getBattleStatistics(): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      const statisticsResult = await this.battleRepository.getStatistics();
      if (XResultUtils.isFailure(statisticsResult)) {
        // 返回默认统计信息而不是错误
        const defaultStats = {
          totalRooms: 0,
          activeRooms: 0,
          finishedRooms: 0,
          todayBattles: 0,
          timestamp: new Date(),
        };
        this.logger.warn(`获取战斗统计信息失败，返回默认值: ${statisticsResult.message}`);
        return XResultUtils.ok(defaultStats);
      }

      return XResultUtils.ok(statisticsResult.data);
    }, {
      reason: 'get_battle_statistics'
    });
  }

  /**
   * 清理过期房间
   */
  async cleanExpiredRooms(): Promise<XResult<number>> {
    return this.executeBusinessOperation(async () => {
      const cleanResult = await this.battleRepository.cleanExpiredRooms(24); // 清理24小时前的房间
      if (XResultUtils.isFailure(cleanResult)) {
        this.logger.error(`清理过期房间失败: ${cleanResult.message}`);
        return XResultUtils.ok(0); // 返回0表示没有清理任何房间
      }

      return XResultUtils.ok(cleanResult.data);
    }, {
      reason: 'clean_expired_rooms'
    });
  }

  /**
   * 🏆 处理战斗奖励发放
   * 基于old项目: 战斗结束后的奖励发放逻辑
   *
   * 职责：
   * 1. 从战斗结果中提取奖励信息（已由BattleRecordGenerator计算）
   * 2. 调用其他微服务发放奖励
   * 3. 记录奖励日志
   */
  async processBattleRewards(
    battleResult: any, // 包含奖励信息的战斗结果
    playerId: string
  ): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      // 🔧 第一步：从战斗结果中提取奖励信息
      const battleEndInfo = battleResult.battleEndInfo;
      if (!battleEndInfo) {
        this.logger.warn('战斗结果中没有奖励信息');
        return XResultUtils.ok({ rewards: null, distributed: false });
      }

      this.logger.debug(`开始发放战斗奖励: playerId=${playerId}`);

      // 🔧 第二步：发放奖励（调用其他微服务）
      const distributionResults = [];

      // 发放经验奖励
      if (battleEndInfo.rewards?.experience > 0) {
        try {
          // 这里应该调用角色服务发放经验
          // const expResult = await this.microserviceClient.call('character', 'addExperience', {
          //   playerId,
          //   experience: battleEndInfo.rewards.experience
          // });
          distributionResults.push({
            type: 'experience',
            amount: battleEndInfo.rewards.experience,
            success: true
          });
        } catch (error) {
          this.logger.error('发放经验奖励失败', error);
          distributionResults.push({
            type: 'experience',
            amount: battleEndInfo.rewards.experience,
            success: false
          });
        }
      }

      // 发放金币奖励
      if (battleEndInfo.rewards?.coins > 0) {
        try {
          // 这里应该调用经济服务发放金币
          // const coinResult = await this.microserviceClient.call('economy', 'addCoins', {
          //   playerId,
          //   coins: battleEndInfo.rewards.coins
          // });
          distributionResults.push({
            type: 'coins',
            amount: battleEndInfo.rewards.coins,
            success: true
          });
        } catch (error) {
          this.logger.error('发放金币奖励失败', error);
          distributionResults.push({
            type: 'coins',
            amount: battleEndInfo.rewards.coins,
            success: false
          });
        }
      }

      // 发放物品奖励
      if (battleEndInfo.lootItemList && battleEndInfo.lootItemList.length > 0) {
        try {
          // 这里应该调用背包服务发放物品
          // const itemResult = await this.microserviceClient.call('inventory', 'addItems', {
          //   playerId,
          //   items: battleEndInfo.lootItemList
          // });
          distributionResults.push({
            type: 'items',
            items: battleEndInfo.lootItemList,
            success: true
          });
        } catch (error) {
          this.logger.error('发放物品奖励失败', error);
          distributionResults.push({
            type: 'items',
            items: battleEndInfo.lootItemList,
            success: false
          });
        }
      }

      // 发放商业赛奖励
      if (battleEndInfo.businessReward) {
        try {
          // 这里应该调用经济服务发放商业赛奖励
          // const businessResult = await this.microserviceClient.call('economy', 'processBusinessReward', {
          //   playerId,
          //   reward: battleEndInfo.businessReward
          // });
          distributionResults.push({
            type: 'business',
            reward: battleEndInfo.businessReward,
            success: true
          });
        } catch (error) {
          this.logger.error('发放商业赛奖励失败', error);
          distributionResults.push({
            type: 'business',
            reward: battleEndInfo.businessReward,
            success: false
          });
        }
      }

      const allSuccess = distributionResults.every(r => r.success);
      this.logger.debug(`奖励发放完成: 成功=${allSuccess}, 结果数=${distributionResults.length}`);

      return XResultUtils.ok({
        rewards: battleEndInfo,
        distributionResults,
        distributed: allSuccess
      });
    }, {
      reason: 'process_battle_rewards'
    });
  }

  /**
   * 转换BattleHero[]为BattleHeroInfo[]
   * 用于解决类型不匹配问题
   */
  private convertBattleHeroesToBattleHeroInfo(heroes: any[]): any[] {
    return heroes.map(hero => ({
      heroId: hero.heroId || hero.heroid,
      attack: hero.attack || 0,
      defend: hero.defend || 0,
      speed: hero.speed || 0,
      power: hero.power || 0,
      technique: hero.technique || 0,
      position: hero.position,
      level: hero.level || 1,
      skills: hero.activeSkills || hero.skills || []
    }));
  }



  /**
   * 转换BattleHero为HeroSnapshoot
   * 用于客户端回放显示参战球员信息
   */
  private convertToHeroSnapshots(heroes: any[]): any[] {
    if (!heroes || heroes.length === 0) {
      // 如果没有球员数据，创建11个默认球员
      return Array.from({ length: 11 }, (_, index) => ({
        heroId: `default_${index + 1}`,
        name: `球员${index + 1}`,
        position: index === 0 ? 'GK' : (index <= 4 ? 'DF' : (index <= 8 ? 'MF' : 'FW')),
        rating: 1
      }));
    }

    // 确保有11名球员
    const snapshots = heroes.slice(0, 11).map(hero => ({
      heroId: hero.heroId || hero.uid || `hero_${Math.random()}`,
      name: hero.name || '未知球员',
      position: hero.position || 'MF',
      rating: Math.max(1, hero.rating || hero.overallRating || 1)
    }));

    // 如果不足11人，补充默认球员
    while (snapshots.length < 11) {
      const index = snapshots.length;
      snapshots.push({
        heroId: `default_${index + 1}`,
        name: `球员${index + 1}`,
        position: index === 0 ? 'GK' : (index <= 4 ? 'DF' : (index <= 8 ? 'MF' : 'FW')),
        rating: 1
      });
    }

    return snapshots;
  }

  /**
   * 创建BattleRoom记录（仅记录战斗开始状态）
   * 重构版本：不包含复杂的队伍数据转换
   */
  private async createBattleRoomRecord(roomId: string, dto: PveBattleDto): Promise<XResult<BattleRoom>> {
    const battleRoomData: Partial<BattleRoom> = {
      roomId: roomId,
      battleType: dto.battleType,
      status: RoomStatus.ACTIVE,
      // createdAt 由schema自动生成
      // 简化的队伍信息，仅记录基本标识
      teamA: {
        characterId: dto.characterId,
        teamType: TeamType.TeamA,
        teamName: dto.characterBattleData.teamName || `队伍${dto.characterId}`,
        rating: 0, // 初始值，战斗后更新
        formationId: dto.characterBattleData.formationId || 442101,
        attackTacticId: dto.characterBattleData.tactic || 101,
        defendTacticId: dto.characterBattleData.tactic || 101,
        heroes: this.convertToHeroSnapshots(dto.characterBattleData.heroes || [])
      },
      teamB: {
        characterId: 'AI',
        teamType: TeamType.TeamB,
        teamName: dto.enemyBattleData.teamName || 'AI队伍',
        rating: 0,
        formationId: dto.enemyBattleData.formationId || 442101,
        attackTacticId: dto.enemyBattleData.tactic || 101,
        defendTacticId: dto.enemyBattleData.tactic || 101,
        heroes: this.convertToHeroSnapshots(dto.enemyBattleData.heroes || [])
      }
    };

    const createResult = await this.battleRepository.create(battleRoomData);
    if (XResultUtils.isFailure(createResult)) {
      return XResultUtils.error(`创建战斗记录失败: ${createResult.message}`, createResult.code);
    }

    return XResultUtils.ok(createResult.data);
  }

  /**
   * 🔧 转换队伍统计数据
   * 从BattleResult.statistics.teamA/teamB转换为数据库格式
   */
  private convertTeamStatistics(teamStats: any): any {
    try {
      if (!teamStats) {
        return {
          shotNum: 0, ctrlBallPer: 0, breakPer: 0, bestBaller: '', bestBallerScore: 0
        };
      }

      // 计算突破成功率
      const breakPer = teamStats.breaks > 0 ?
        Math.round((teamStats.successfulBreaks || 0) / teamStats.breaks * 100) : 0;

      // 获取最佳球员
      let bestBaller = '';
      let bestBallerScore = 0;
      if (teamStats.ballerScoreMap && teamStats.ballerScoreMap.size > 0) {
        teamStats.ballerScoreMap.forEach((score: number, heroId: string) => {
          if (score > bestBallerScore) {
            bestBallerScore = score;
            bestBaller = heroId; // 简化实现，实际应该获取球员姓名
          }
        });
      }

      return {
        shotNum: teamStats.shots || 0,
        ctrlBallPer: teamStats.possession || 0,
        breakPer: breakPer,
        bestBaller: bestBaller,
        bestBallerScore: Math.round(bestBallerScore * 10) / 10, // 保留一位小数
        heroRatings: this.convertMapToObject(teamStats.ballerScoreMap) // 🔧 添加球员评分
      };
    } catch (error) {
      this.logger.error('转换队伍统计数据失败', error);
      return {
        shotNum: 0, ctrlBallPer: 0, breakPer: 0, bestBaller: '', bestBallerScore: 0
      };
    }
  }





  /**
   * 🔧 从战斗结果中生成进球记录
   * 从BattleResult.statistics.teamA/teamB.goalEventMap中获取
   */
  private generateGoalRecords(battleResult: BattleResult): any[] {
    try {
      const goals: any[] = [];

      // 从teamA的goalEventMap中提取进球记录
      if (battleResult.statistics?.teamA?.goalEventMap) {
        battleResult.statistics.teamA.goalEventMap.forEach((goalEvent: any, time: string) => {
          goals.push({
            time: parseInt(time) || 0,
            ballerHeroId: goalEvent.ballerHeroId || '',
            assistHeroId: goalEvent.assistHeroId || '', // 添加助攻球员
            teamType: 'teamA'
          });
        });
      }

      // 从teamB的goalEventMap中提取进球记录
      if (battleResult.statistics?.teamB?.goalEventMap) {
        battleResult.statistics.teamB.goalEventMap.forEach((goalEvent: any, time: string) => {
          goals.push({
            time: parseInt(time) || 0,
            ballerHeroId: goalEvent.ballerHeroId || '',
            assistHeroId: goalEvent.assistHeroId || '', // 添加助攻球员
            teamType: 'teamB'
          });
        });
      }

      // 按时间排序
      goals.sort((a, b) => a.time - b.time);

      this.logger.debug(`生成进球记录: ${goals.length}个进球`);
      return goals;
    } catch (error) {
      this.logger.error('生成进球记录失败', error);
      return [];
    }
  }

  /**
   * 更新BattleRoom记录（保存战斗结果和最终队伍数据）
   */
  private async updateBattleRoomResult(roomId: string, battleResult: BattleResult): Promise<XResult<void>> {
    const updateData: Partial<BattleRoom> = {
      status: RoomStatus.FINISHED,
      finishedAt: new Date(),

      // 更新队伍的评分（基于最终比分计算）
      'teamA.rating': Math.max(0, battleResult.finalScore?.teamA * 100 || 0),
      'teamB.rating': Math.max(0, battleResult.finalScore?.teamB * 100 || 0),

      // 战斗结果 - 使用新的BattleResult schema结构
      result: {
        roomId: roomId,
        battleType: battleResult.battleType || 'PVE_LEAGUE',
        homeScore: battleResult.finalScore?.teamA || 0,
        awayScore: battleResult.finalScore?.teamB || 0,
        winner: this.determineWinner(
          battleResult.finalScore?.teamA || 0,
          battleResult.finalScore?.teamB || 0
        ),
        battleTime: new Date(),
        totalTime: battleResult.statistics?.totalTime || 0,
        totalRounds: battleResult.statistics?.totalRounds || 0,

        // 队伍统计数据 - 从statistics.teamA/teamB转换
        teamAStats: this.convertTeamStatistics(battleResult.statistics?.teamA),
        teamBStats: this.convertTeamStatistics(battleResult.statistics?.teamB),

        // 进球记录 - 从统计数据中生成
        goals: this.generateGoalRecords(battleResult),

        // 奖励信息 - BattleResult中没有rewards字段
        rewards: null,

        // 掉落物品 - BattleResult中没有lootItems字段
        lootItems: [],

        // 战斗回合数据
        rounds: battleResult.battleRecord?.battleRoundInfo || [],

        // 技能记录 - 从battleResult.skillRecords获取
        skillRecords: battleResult.skillRecords || []
      }
    } as Partial<BattleRoom>;

    this.logger.debug(`准备更新数据: ${JSON.stringify({
      status: updateData.status,
      finishedAt: !!updateData.finishedAt,
      teamARating: updateData['teamA.rating'],
      teamBRating: updateData['teamB.rating'],
      resultExists: !!updateData.result,
      roundsCount: updateData.result?.rounds?.length || 0
    }, null, 2)}`);

    // 更新战斗房间记录
    const updateResult = await this.battleRepository.updateByRoomId(roomId, updateData);
    if (XResultUtils.isFailure(updateResult)) {
      this.logger.error(`更新战斗记录失败: ${updateResult.message}`, updateResult);
      return XResultUtils.error(`更新战斗记录失败: ${updateResult.message}`, updateResult.code);
    }

    this.logger.log(`战斗记录更新成功: ${roomId}`);

    return XResultUtils.ok(undefined);
  }

  /**
   * 创建PVP BattleRoom记录（仅记录战斗开始状态）
   */
  private async createPvpBattleRoomRecord(roomId: string, dto: PvpBattleDto): Promise<XResult<BattleRoom>> {
    const battleRoomData: Partial<BattleRoom> = {
      roomId: roomId,
      battleType: dto.battleType,
      status: RoomStatus.ACTIVE,
      // createdAt 由schema自动生成
      // 简化的队伍信息，仅记录基本标识
      teamA: {
        characterId: dto.homeCharacterId,
        teamType: TeamType.TeamA,
        teamName: dto.homeBattleData.teamName || `队伍${dto.homeCharacterId}`,
        rating: 0, // 初始值，战斗后更新
        formationId: dto.homeBattleData.formationId || 442101,
        attackTacticId: dto.homeBattleData.tactic || 101,
        defendTacticId: dto.homeBattleData.tactic || 101,
        heroes: this.convertToHeroSnapshots(dto.homeBattleData.heroes || [])
      },
      teamB: {
        characterId: dto.awayCharacterId,
        teamType: TeamType.TeamB,
        teamName: dto.awayBattleData.teamName || `队伍${dto.awayCharacterId}`,
        rating: 0,
        formationId: dto.awayBattleData.formationId || 442101,
        attackTacticId: dto.awayBattleData.tactic || 101,
        defendTacticId: dto.awayBattleData.tactic || 101,
        heroes: this.convertToHeroSnapshots(dto.awayBattleData.heroes || [])
      }
    };

    const createResult = await this.battleRepository.create(battleRoomData);
    if (XResultUtils.isFailure(createResult)) {
      return XResultUtils.error(`创建PVP战斗记录失败: ${createResult.message}`, createResult.code);
    }

    return XResultUtils.ok(createResult.data);
  }



  /**
   * 🔧 将Map转换为普通对象（用于数据库存储）
   */
  private convertMapToObject(map?: Map<string, number>): Record<string, number> {
    if (!map || map.size === 0) {
      return {};
    }

    const obj: Record<string, number> = {};
    map.forEach((rating, heroId) => {
      obj[heroId] = Math.round(rating); // 四舍五入为整数
    });

    return obj;
  }
}
