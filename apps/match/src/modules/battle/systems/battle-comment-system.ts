import { Logger } from '@nestjs/common';
import { GameConfigFacade } from '@libs/game-config';
import { ActionDefinition, CommentDefinition } from '@libs/game-config';
import { BattleTeam, AttackMode } from '../types/battle-data.types';

// 评论系统专用类型定义
interface TeamInfo {
  teamName?: string;
  playerName?: string;
  heroes?: HeroInfo[];
}

interface HeroInfo {
  heroId?: string;
  heroUid?: string;
  Uid?: string;
  id?: string;
  name?: string;
  Name?: string;
  heroName?: string;
}

interface PeriodInfo {
  actionID: number;
  success: boolean;
  attackMode?: number;
  period?: number;
  A1Info?: HeroActionInfo;
  A2Info?: HeroActionInfo;
  BInfo?: HeroActionInfo;
  GKInfo?: HeroActionInfo;
}

interface HeroActionInfo {
  heroUid?: string;
  heroId?: string;
  name?: string;
  heroName?: string;
}

interface PeriodResult {
  success: boolean;
  period?: number;
}

interface BattleDataInfo {
  teamA: TeamInfo & { score?: number; statistic?: TeamStatistic };
  teamB: TeamInfo & { score?: number; statistic?: TeamStatistic };
  battleTime?: number;
  roundIndex?: number;
}

interface TeamStatistic {
  shots?: number;
  shotsOnTarget?: number;
}

/**
 * 战斗评论系统 - 完全重构版本
 * 基于old项目room.js的评论生成逻辑，完整集成GameConfigFacade
 *
 * 🔧 核心功能：
 * - 从Action配置表读取评论组ID (StartComment/SucComment/FailComment)
 * - 从Comment配置表按GroupID筛选评论列表
 * - 随机选择评论ID，完全对齐old项目逻辑
 * - 支持old项目占位符格式 (#A1#、#B#、#GK#等)
 * - 提供评论文本生成和球员名称替换
 *
 * 🎯 架构优势：
 * - 完整的配置表集成
 * - 类型安全的ActionDefinition和CommentDefinition
 * - 完善的错误处理和日志记录
 */
export class BattleCommentSystem {
  private readonly logger = new Logger(BattleCommentSystem.name);



  constructor(private readonly gameConfig: GameConfigFacade) {}

  /**
   * 🔧 计算评论 - 完全重构版本
   * 基于old项目: Room.prototype.calcComment
   *
   * 核心逻辑：
   * 1. 从Action配置表获取评论组ID
   * 2. 从Comment配置表按GroupID筛选评论
   * 3. 随机选择评论ID
   * 4. 返回开始评论ID和结果评论ID
   */
  async calcComment(
    actionID: number,
    period: number,
    isSuccess: boolean,
    attackMode: number,
    participatingPlayers: string[]
  ): Promise<{ startCommentID: number; resultCommentID: number }> {
    try {
      // 第一步：获取Action配置
      const actionConfig = await this.getActionConfig(actionID);
      if (!actionConfig) {
        this.logger.warn(`Action配置不存在: ${actionID}`);
        return { startCommentID: 0, resultCommentID: 0 };
      }

      // 第二步：获取评论组ID
      const commentGroups = {
        startGroupId: actionConfig.startComment,
        sucGroupId: actionConfig.sucComment,
        failGroupId: actionConfig.failComment
      };

      this.logger.debug(`Action ${actionID} 评论组: 开始=${commentGroups.startGroupId}, 成功=${commentGroups.sucGroupId}, 失败=${commentGroups.failGroupId}`);

      // 第三步：获取开始评论ID
      const startCommentID = await this.getCommentIDFromGroup(commentGroups.startGroupId);

      // 第四步：获取结果评论ID
      const resultGroupId = isSuccess ? commentGroups.sucGroupId : commentGroups.failGroupId;
      const resultCommentID = await this.getCommentIDFromGroup(resultGroupId);

      this.logger.debug(`生成评论: 动作=${actionID}, 阶段=${period}, 成功=${isSuccess}, 开始评论=${startCommentID}, 结果评论=${resultCommentID}`);

      return { startCommentID, resultCommentID };
    } catch (error) {
      this.logger.error('计算评论失败', error);
      return { startCommentID: 0, resultCommentID: 0 };
    }
  }

  /**
   * 🔧 获取Action配置
   * 从Action配置表获取指定动作的配置信息
   */
  private async getActionConfig(actionID: number): Promise<ActionDefinition | null> {
    try {
      // 从配置表查询 - 使用findOneBy方法按actionId字段查找
      const actionConfig = await this.gameConfig.action.findOneBy('actionId', actionID);
      return actionConfig;
    } catch (error) {
      this.logger.error(`获取Action配置失败: ${actionID}`, error);
      return null;
    }
  }

  /**
   * 🔧 从评论组获取评论ID - 核心逻辑
   * 基于old项目逻辑：从Comment配置表按GroupID筛选，然后随机选择
   *
   * 对应old项目代码：
   * ```javascript
   * for(let key in commentConfig) {
   *   if(commentConfig[key].GroupID === group.startGroup.id) {
   *     group.startGroup.array.push(commentConfig[key].ID);
   *   }
   * }
   * let tmpRand = utils.random(1, group.startGroup.array.length);
   * commentID.startID = group.startGroup.array[tmpRand-1];
   * ```
   */
  private async getCommentIDFromGroup(groupId: number): Promise<number> {
    try {
      // 如果groupId为0，返回0（old项目中的处理方式）
      if (groupId === 0) {
        return 0;
      }

      // 获取该组的所有评论
      const comments = await this.getCommentsByGroupId(groupId);

      if (comments.length === 0) {
        this.logger.warn(`评论组 ${groupId} 中没有找到评论`);
        return 0;
      }

      // 随机选择一个评论 - 对应old项目的utils.random(1, length)
      const randomIndex = Math.floor(Math.random() * comments.length);
      const selectedComment = comments[randomIndex];

      this.logger.debug(`从评论组 ${groupId} 中选择评论: ${selectedComment.id}`);
      return selectedComment.id;

    } catch (error) {
      this.logger.error(`从评论组获取评论ID失败: ${groupId}`, error);
      return 0;
    }
  }

  /**
   * 🔧 按GroupID获取评论列表
   * 从Comment配置表按groupId字段筛选评论
   */
  private async getCommentsByGroupId(groupId: number): Promise<CommentDefinition[]> {
    try {
      // 从配置表查询 - 使用filter方法按groupId筛选
      const comments = await this.gameConfig.comment.filter(
        (comment: CommentDefinition) => comment.groupId === groupId
      );

      this.logger.debug(`评论组 ${groupId} 包含 ${comments.length} 条评论`);
      return comments;

    } catch (error) {
      this.logger.error(`获取评论组失败: ${groupId}`, error);
      return [];
    }
  }

  /**
   * 🔧 生成完整评论文本 - 重构版本
   * 将评论ID转换为实际的评论文本，支持old项目占位符格式
   *
   * 支持的占位符格式：
   * - #A1# : 主攻球员
   * - #A2# : 协攻球员
   * - #B#  : 防守球员
   * - #GK# : 门将
   *
   * 兼容格式：
   * - {player1}, {player2} : 通用球员占位符
   */
  async generateCommentText(commentID: number, playerNames: string[] = []): Promise<string> {
    try {
      // 获取评论配置
      const commentConfig = await this.getCommentConfig(commentID);

      if (!commentConfig) {
        return `评论 ${commentID}`;
      }

      // 获取评论模板文本
      let commentText = commentConfig.comment;

      // 替换old项目格式的占位符 (#A1#, #A2#, #B#, #GK#)
      commentText = this.replaceOldProjectPlaceholders(commentText, playerNames);

      // 替换通用格式的占位符 ({player1}, {player2})
      commentText = this.replaceGenericPlaceholders(commentText, playerNames);

      return commentText;
    } catch (error) {
      this.logger.warn(`生成评论文本失败: ${commentID}`, error);
      return `评论 ${commentID}`;
    }
  }

  /**
   * 🔧 获取评论配置
   */
  private async getCommentConfig(commentID: number): Promise<CommentDefinition | null> {
    try {
      const commentConfig = await this.gameConfig.comment.get(commentID);
      return commentConfig;
    } catch (error) {
      this.logger.error(`获取评论配置失败: ${commentID}`, error);
      return null;
    }
  }

  /**
   * 🔧 替换old项目占位符 - 核心功能
   * 处理 #A1#、#A2#、#B#、#GK# 格式的占位符
   */
  private replaceOldProjectPlaceholders(text: string, playerNames: string[]): string {
    let result = text;

    // 定义占位符映射 - 基于old项目的实际使用
    const placeholderMap: { [key: string]: number } = {
      '#A1#': 0,  // 主攻球员
      '#A2#': 1,  // 协攻球员
      '#B#': 2,   // 防守球员
      '#GK#': 3   // 门将
    };

    // 替换所有占位符
    for (const [placeholder, index] of Object.entries(placeholderMap)) {
      if (result.includes(placeholder)) {
        const playerName = playerNames[index] || `球员${index + 1}`;
        result = result.replace(new RegExp(placeholder.replace(/[#]/g, '\\#'), 'g'), playerName);
      }
    }

    return result;
  }

  /**
   * 🔧 替换通用占位符
   * 处理 {player1}、{player2} 格式的占位符
   */
  private replaceGenericPlaceholders(text: string, playerNames: string[]): string {
    let result = text;

    // 替换 {player1}, {player2}, {player3}, {player4} 等
    playerNames.forEach((name, index) => {
      const placeholder = `{player${index + 1}}`;
      result = result.replace(new RegExp(placeholder.replace(/[{}]/g, '\\$&'), 'g'), name);
    });

    return result;
  }

  /**
   * 🔧 获取球员名称 - 增强版本
   * 从队伍数据中提取指定球员的名称，支持多种字段格式
   */
  getPlayerName(team: TeamInfo, heroUid: string): string {
    try {
      if (!team.heroes || !Array.isArray(team.heroes)) {
        return `球员${heroUid}`;
      }

      // 支持多种字段格式的球员查找
      const hero = team.heroes.find((h: HeroInfo) =>
        h.heroId === heroUid ||
        h.Uid === heroUid ||
        h.heroUid === heroUid ||
        h.id === heroUid
      );

      if (hero) {
        // 支持多种名称字段
        return hero.name || hero.Name || hero.heroName || `球员${heroUid}`;
      }

      return `球员${heroUid}`;
    } catch (error) {
      this.logger.warn(`获取球员名称失败: ${heroUid}`, error);
      return `球员${heroUid}`;
    }
  }

  /**
   * 🔧 批量生成回合评论 - 重构版本
   * 基于old项目的批量评论生成逻辑，完整集成配置表
   */
  async generateBatchComments(
    roundIndex: number,
    attackerTeam: TeamInfo,
    defenderTeam: TeamInfo,
    periodInfos: PeriodInfo[]
  ): Promise<{
    comments: any[];
    roundSummary: string;
    roundIndex: number;
    isGoal: boolean;
  }> {
    try {
      const comments = [];

      for (let i = 0; i < periodInfos.length; i++) {
        const periodInfo = periodInfos[i];

        if (periodInfo.actionID && periodInfo.actionID > 0) {
          // 提取球员信息
          const players = this.extractPlayersFromPeriod(periodInfo);
          const playerNames = players.map(p => p.name);

          // 计算评论ID
          const comment = await this.calcComment(
            periodInfo.actionID,
            i,
            periodInfo.success || false,
            periodInfo.attackMode || 1,
            playerNames
          );

          // 生成评论文本
          const startCommentText = await this.generateCommentText(comment.startCommentID, playerNames);
          const resultCommentText = await this.generateCommentText(comment.resultCommentID, playerNames);

          // 扩展评论对象
          const extendedComment = {
            ...comment,
            startCommentText,
            resultCommentText,
            players,
            roundIndex,
            periodIndex: i,
            actionID: periodInfo.actionID,
            attackMode: periodInfo.attackMode || 1,
            success: periodInfo.success || false,
            timestamp: Date.now()
          };

          comments.push(extendedComment);
        }
      }

      // 生成回合摘要
      const isGoal = periodInfos.some(p => p.success && p.period === 2); // 射门阶段成功即为进球
      const roundSummary = this.generateRoundSummary(
        roundIndex,
        attackerTeam,
        defenderTeam,
        periodInfos[0]?.attackMode || 1,
        periodInfos,
        isGoal
      );

      this.logger.debug(`批量生成评论完成: ${comments.length}条，回合摘要: ${roundSummary}`);

      return {
        comments,
        roundSummary,
        roundIndex,
        isGoal
      };

    } catch (error) {
      this.logger.error('批量生成评论失败', error);
      return {
        comments: [],
        roundSummary: '',
        roundIndex,
        isGoal: false
      };
    }
  }

  /**
   * 🔧 从阶段信息中提取球员 - 增强版本
   * 辅助方法：提取参与该阶段的球员信息，支持完整的球员角色
   */
  private extractPlayersFromPeriod(periodInfo: PeriodInfo): Array<{
    role: string;
    heroUid: string;
    name: string;
  }> {
    try {
      const players = [];

      // 提取A1球员（主要进攻球员）
      if (periodInfo.A1Info && (periodInfo.A1Info.heroId || periodInfo.A1Info.heroId)) {
        players.push({
          role: 'A1',
          heroId: periodInfo.A1Info.heroId || periodInfo.A1Info.heroId,
          name: periodInfo.A1Info.name || periodInfo.A1Info.heroName || '球员A1'
        });
      }

      // 提取A2球员（协助进攻球员）
      if (periodInfo.A2Info && (periodInfo.A2Info.heroId || periodInfo.A2Info.heroId)) {
        players.push({
          role: 'A2',
          heroId: periodInfo.A2Info.heroId || periodInfo.A2Info.heroId,
          name: periodInfo.A2Info.name || periodInfo.A2Info.heroName || '球员A2'
        });
      }

      // 提取B球员（防守球员）
      if (periodInfo.BInfo && (periodInfo.BInfo.heroId || periodInfo.BInfo.heroId)) {
        players.push({
          role: 'B',
          heroId: periodInfo.BInfo.heroId || periodInfo.BInfo.heroId,
          name: periodInfo.BInfo.name || periodInfo.BInfo.heroName || '球员B'
        });
      }

      // 提取门将
      if (periodInfo.GKInfo && (periodInfo.GKInfo.heroId || periodInfo.GKInfo.heroId)) {
        players.push({
          role: 'GK',
          heroId: periodInfo.GKInfo.heroId || periodInfo.GKInfo.heroId,
          name: periodInfo.GKInfo.name || periodInfo.GKInfo.heroName || '门将'
        });
      }

      return players;

    } catch (error) {
      this.logger.warn('提取球员信息失败', error);
      return [];
    }
  }

  /**
   * 🔧 生成回合评论摘要 - 增强版本
   * 基于阶段结果生成简洁的回合摘要，用于战报显示
   */
  generateRoundSummary(
    roundIndex: number,
    attackerTeam: TeamInfo,
    defenderTeam: TeamInfo,
    attackMode: number,
    periodResults: PeriodResult[],
    isGoal: boolean
  ): string {
    try {
      const attackerName = attackerTeam.teamName || attackerTeam.playerName || 'Team A';
      const defenderName = defenderTeam.teamName || defenderTeam.playerName || 'Team B';

      let summary = `第${roundIndex + 1}回合：${attackerName}发起进攻`;

      // 根据攻击模式添加描述
      const attackModeDesc = this.getAttackModeDescription(attackMode);
      if (attackModeDesc) {
        summary += `(${attackModeDesc})`;
      }

      // 根据阶段结果生成摘要
      if (periodResults.length >= 1 && periodResults[0].success) {
        summary += '，成功发起';

        if (periodResults.length >= 2) {
          if (periodResults[1].success) {
            summary += '，突破成功';

            if (periodResults.length >= 3) {
              if (periodResults[2].success) {
                summary += '，射门得分！';
              } else {
                summary += '，射门被扑出';
              }
            }
          } else {
            summary += '，突破失败';
          }
        }
      } else {
        summary += '，发起失败';
      }

      return summary;
    } catch (error) {
      this.logger.error('生成回合摘要失败', error);
      return `第${roundIndex + 1}回合`;
    }
  }

  /**
   * 🔧 获取攻击模式描述
   * 将攻击模式数字转换为中文描述
   */
  private getAttackModeDescription(attackMode: number): string {
    const attackModeMap: { [key: number]: string } = {
      1: '头球',
      2: '远射',
      3: '推射',
      4: '抢点',
      5: '吊射',
      6: '单刀',
      7: '任意球',
      8: '角球',
      9: '点球'
    };

    return attackModeMap[attackMode] || '';
  }







  /**
   * 🔧 生成比赛总结评论 - 增强版本
   * 基于old项目的比赛总结生成逻辑，提供完整的比赛统计信息
   */
  generateMatchSummary(battleData: BattleDataInfo): string {
    try {
      const teamAName = battleData.teamA.teamName || battleData.teamA.playerName || 'Team A';
      const teamBName = battleData.teamB.teamName || battleData.teamB.playerName || 'Team B';
      const scoreA = battleData.teamA.score || 0;
      const scoreB = battleData.teamB.score || 0;
      const totalTime = Math.floor((battleData.battleTime || 0) / 60); // 转换为分钟

      let summary = `⚽ 比赛结束！${teamAName} ${scoreA} : ${scoreB} ${teamBName}`;

      // 添加比赛时长
      summary += `\n⏱️ 比赛用时：${totalTime}分钟`;

      // 添加获胜方信息
      if (scoreA > scoreB) {
        summary += `\n🏆 ${teamAName}获得胜利！`;
      } else if (scoreB > scoreA) {
        summary += `\n🏆 ${teamBName}获得胜利！`;
      } else {
        summary += `\n🤝 双方战成平局！`;
      }

      // 添加精彩瞬间统计
      const totalRounds = (battleData.roundIndex || 0) + 1;
      summary += `\n📊 本场比赛共进行了${totalRounds}个回合`;

      // 添加详细统计（如果有）
      if (battleData.teamA.statistic && battleData.teamB.statistic) {
        const statsA = battleData.teamA.statistic;
        const statsB = battleData.teamB.statistic;

        summary += `\n📈 射门统计：${teamAName} ${statsA.shots || 0} - ${statsB.shots || 0} ${teamBName}`;
        summary += `\n🎯 射正统计：${teamAName} ${statsA.shotsOnTarget || 0} - ${statsB.shotsOnTarget || 0} ${teamBName}`;
      }

      return summary;

    } catch (error) {
      this.logger.error('生成比赛总结失败', error);
      return '⚽ 比赛结束！';
    }
  }


}
