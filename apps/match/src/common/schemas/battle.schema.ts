import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { HeroPosition, BattleType } from '@libs/game-constants';

// 数组长度验证函数
function arrayLimit(val: any[]): boolean {
  return val.length <= 3;
}

// 球员数量验证函数 (11人)
function arrayLimit11(val: any[]): boolean {
  return val.length === 11;
}



// 队伍类型
export enum TeamType {
  TeamA = 'teamA',
  TeamB = 'teamB',
}

// 房间状态
export enum RoomStatus {
  ACTIVE = 'active',
  FINISHED = 'finished',
  EXPIRED = 'expired',
}

/**
 * 球员快照实体
 * @description 战斗前的核心信息, 对应 optimized-battle-replay-structure.md #3
 */
@Schema({ _id: false, versionKey: false })
export class HeroSnapshoot {
  @Prop({ required: true, index: true, maxlength: 50 })
  heroId: string;                                  // 球员ID (原heroUid)

  @Prop({ required: true, maxlength: 30 })
  name: string;                                    // 球员姓名

  @Prop({ type: String, enum: Object.values(HeroPosition), required: true })
  position: HeroPosition;                          // 球员位置

  @Prop({ required: true, min: 1, max: 100 })
  rating: number;                                  // 球员评分
}

/**
 * 队伍统计信息
 * @description 对应原 battleEndInfo.stInfos
 */
@Schema({ _id: false, versionKey: false })
export class TeamStatistics {
  @Prop({ type: String, enum: Object.values(TeamType), required: true })
  teamType: TeamType;                              // 队伍类型

  @Prop({ required: true, min: 0, max: 50 })
  shotNum: number;                                 // 射门次数

  @Prop({ required: true, min: 0, max: 100 })
  ctrlBallPer: number;                             // 控球率 (百分比)

  @Prop({ required: true, min: 0, max: 100 })
  breakPer: number;                                // 突破成功率 (百分比)

  @Prop({ required: true, min: 0, max: 20 })
  placeKickNum: number;                            // 定位球次数

  @Prop({ required: true, maxlength: 30 })
  bestBaller: string;                              // 最佳球员姓名

  @Prop({ type: Object, default: {} })
  heroRatings: Record<string, number>;             // 球员评分 heroId -> rating
}

/**
 * 进球记录
 * @description 对应原 battleEndInfo.goalRecord
 */
@Schema({ _id: false, versionKey: false })
export class GoalRecord {
  @Prop({ required: true, min: 0, max: 5400 })
  time: number;                                    // 进球时间 (秒)

  @Prop({ required: true, maxlength: 30 })
  ballerName: string;                              // 进球球员姓名

  @Prop({ type: String, enum: Object.values(TeamType), required: true })
  teamType: TeamType;                              // 进球队伍

  @Prop({ maxlength: 50 })
  ballerHeroId?: string;                           // 进球球员ID

  @Prop({ maxlength: 30 })
  assistName?: string;                             // 助攻球员姓名

  @Prop({ maxlength: 50 })
  assistHeroId?: string;                           // 助攻球员ID
}

/**
 * 队伍奖励
 */
@Schema({ _id: false, versionKey: false })
export class TeamReward {
  @Prop({ required: true, min: 0, max: 100000 })
  exp: number;                                     // 经验值

  @Prop({ required: true, min: 0, max: 1000000 })
  coins: number;                                   // 金币

  @Prop({ required: true, min: -10000, max: 10000 })
  fansChangeNum: number;                           // 球迷变化数量 (可为负数)
}

/**
 * 战斗奖励
 * @description 对应原 battleEndInfo.businessReward
 */
@Schema({ _id: false, versionKey: false })
export class BattleRewards {
  @Prop({ type: TeamReward, required: true })
  homeReward: TeamReward;                          // 主队奖励

  @Prop({ type: TeamReward, required: true })
  awayReward: TeamReward;                          // 客队奖励
}

/**
 * 掉落物品
 * @description 对应原 battleEndInfo.lootItemList
 */
@Schema({ _id: false, versionKey: false })
export class LootItem {
  @Prop({ required: true, min: 1 })
  itemId: number;                                  // 物品ID

  @Prop({ required: true, min: 1, max: 999 })
  quantity: number;                                // 数量
}

/**
 * 球员动作信息
 * @description 对应原 A1Info, A2Info, BInfo, GKInfo
 */
@Schema({ _id: false, versionKey: false })
export class HeroActionInfo {
  @Prop({ required: true, maxlength: 50 })
  heroId: string;                                  // 球员ID

  @Prop({ min: 1, max: 20 })
  attrType1?: number;                              // 第一属性类型 (1-20)

  @Prop({ min: 0, max: 200 })
  attrValue1?: number;                             // 第一属性值

  @Prop({ min: -50, max: 100 })
  addValue1?: number;                              // 第一属性加成值

  @Prop({ min: 1, max: 20 })
  attrType2?: number;                              // 第二属性类型 (1-20)

  @Prop({ min: 0, max: 200 })
  attrValue2?: number;                             // 第二属性值

  @Prop({ min: -50, max: 100 })
  addValue2?: number;                              // 第二属性加成值

  @Prop({ min: 1 })
  resId?: number;                                  // 球员资源ID
}

/**
 * 评论信息
 * @description 基于battle-comment-system.ts和CommentDefinition优化的评论结构
 */
@Schema({ _id: false, versionKey: false })
export class Comment {
  @Prop({ required: true, min: 1 })
  commentId: number;                               // 评论Id, 对应Comment.json

  @Prop({ required: true, min: 0 })
  actionId: number;                                // 动作Id

  @Prop({ required: true, min: 0, max: 2 })
  period: number;                                  // 阶段 (0=发起, 1=推进, 2=射门)

  @Prop({ required: true })
  result: boolean;                                 // 动作结果

  @Prop({ required: true, enum: ['start', 'success', 'failure'] })
  type: string;                                    // 评论类型

  @Prop({ required: true, maxlength: 200 })
  text: string;                                    // 评论文本

  @Prop({ type: [Object], default: [] })
  players?: Array<{                                // 参与球员信息
    role: string;                                  // 角色 (A1/A2/B/GK)
    heroId: string;                                // 球员ID
    name: string;                                  // 球员姓名
  }>;

  @Prop({ required: true, default: Date.now })
  timestamp: number;                               // 生成时间戳

  @Prop({ min: 0 })
  roundIndex?: number;                             // 回合索引

  @Prop({ min: 0, max: 2 })
  periodIndex?: number;                            // 阶段索引
}


/**
 * 阶段信息
 * @description 对应原 periodInfo
 */
@Schema({ _id: false, versionKey: false })
export class PeriodInfo {
  @Prop({ type: HeroActionInfo })
  A1Info?: HeroActionInfo;                         // 主要攻击球员

  @Prop({ type: HeroActionInfo })
  A2Info?: HeroActionInfo;                         // 协助攻击球员

  @Prop({ type: HeroActionInfo })
  BInfo?: HeroActionInfo;                          // 防守球员

  @Prop({ type: HeroActionInfo })
  GKInfo?: HeroActionInfo;                         // 门将

  @Prop({ required: true, min: 0, max: 100 })
  actionId: number;                                // 动作ID

  @Prop({ min: 100000, max: 999999 })
  startCommentId?: number;                         // 开始评论ID

  @Prop({ min: 100000, max: 999999 })
  resultCommentId?: number;                        // 结果评论ID

  @Prop({ min: 0, max: 1000 })
  actionPer?: number;                              // 成功率 (千分制)

  @Prop({ min: 1, max: 2 })
  actionResult?: number;                           // 动作结果 (1=成功, 2=失败)

  @Prop({ min: 0, max: 1000 })
  moraleA?: number;                                // A队士气

  @Prop({ min: 0, max: 1000 })
  moraleB?: number;                                // B队士气

  @Prop({ type: [Object] })
  skillEffectList?: any[];                         // 技能效果列表
}

/**
 * 持续性技能记录
 */
@Schema({ _id: false, versionKey: false })
export class DurativeSkillRecord {
  @Prop({ required: true, min: 80000, max: 89999 })
  skillId: number;                               // 技能ID (基于old项目80xxx格式)

  @Prop({ required: true, min: 0, max: 5400 })
  startTime: number;                             // 开始时间 (秒)

  @Prop({ required: true, min: 0, max: 5400 })
  endTime: number;                               // 结束时间 (秒)

  @Prop({ required: true, maxlength: 50 })
  heroId: string;                                // 施放球员ID

  @Prop({ min: 0, max: 50 })
  round?: number;                                // 触发回合

  @Prop({ min: 0, max: 2 })
  period?: number;                               // 触发阶段
}

/**
 * 瞬时性技能记录
 */
@Schema({ _id: false, versionKey: false })
export class InstantSkillRecord {
  @Prop({ required: true, min: 80000, max: 89999 })
  skillId: number;                               // 技能ID (基于old项目80xxx格式)

  @Prop({ required: true, min: 0, max: 50 })
  round: number;                                 // 触发回合

  @Prop({ required: true, maxlength: 50 })
  heroId: string;                                // 施放球员ID

  @Prop({ required: true, min: 0, max: 2 })
  period: number;                                // 触发阶段
}

/**
 * 下次攻击技能记录
 */
@Schema({ _id: false, versionKey: false })
export class NextAttackSkillRecord {
  @Prop({ required: true, min: 80000, max: 89999 })
  skillId: number;                               // 技能ID (基于old项目80xxx格式)

  @Prop({ required: true, maxlength: 50 })
  heroId: string;                                // 施放球员ID

  @Prop({ required: true, min: 0, max: 50 })
  targetRound: number;                           // 目标回合
}

/**
 * 技能记录实体
 * @description 对应原 skillRecord
 */
@Schema({ _id: false, versionKey: false })
export class SkillRecord {
  @Prop({ type: [DurativeSkillRecord], required: true }) 
  durRecord: DurativeSkillRecord[];                // 持续性技能列表

  @Prop({ type: [InstantSkillRecord], required: true }) 
  insRecord: InstantSkillRecord[];                 // 瞬时性技能列表

  @Prop({ type: [NextAttackSkillRecord], required: true }) 
  nextAtkRecord: NextAttackSkillRecord[];          // 下次攻击技能列表
}

/**
 * 战斗回合实体
 * @description 对应原 battleRoundInfo
 */
@Schema({ _id: false, versionKey: false })
export class BattleRound {
  @Prop({ required: true, min: 0, max: 5400 })
  eventTime: number;                             // 事件时间 (秒)

  @Prop({ required: true, min: 0, max: 1000 })
  moraleA: number;                               // A队士气

  @Prop({ required: true, min: 0, max: 1000 })
  moraleB: number;                               // B队士气

  @Prop({ type: String, enum: Object.values(TeamType), required: true })
  attackerType: TeamType;                        // 攻击方

  @Prop({ required: true, min: 1, max: 10 })
  attackMode: number;                            // 攻击模式

  @Prop({ required: true, min: 0, max: 20 })
  scoreA: number;                                // A队比分

  @Prop({ required: true, min: 0, max: 20 })
  scoreB: number;                                // B队比分

  @Prop({ type: [PeriodInfo], required: true, validate: [arrayLimit, '{PATH} 最多3个阶段'] })
  periodInfo: PeriodInfo[];                      // 阶段信息列表

  @Prop({ min: 0, max: 1000000 })
  moraleSlotA?: number;                          // A队士气槽

  @Prop({ min: 0, max: 1000000 })
  moraleSlotB?: number;                          // B队士气槽

  @Prop({ type: [Comment] })
  comments?: Comment[];                          // 评论列表
}

/**
 * 战斗结果实体
 * @description 对应原 BattleReplayData 层级, 整合了 battleEndInfo, battleRoundInfo, skillRecord
 */
@Schema({ _id: false, versionKey: false })
export class BattleResult {
  @Prop({ required: true }) 
  roomId: string;                                // 房间ID

  @Prop({ type: String, enum: Object.values(BattleType), required: true }) 
  battleType: BattleType;                        // 战斗类型

  @Prop({ required: true }) 
  homeScore: number;                             // 主队比分

  @Prop({ required: true }) 
  awayScore: number;                             // 客队比分

  @Prop({ required: true }) 
  winner: number;                                // 胜利方 (0平局 1主队 2客队)

  @Prop({ required: true }) 
  battleTime: Date;                              // 战斗时间

  @Prop({ required: true }) 
  totalTime: number;                             // 总时长

  @Prop({ required: true }) 
  totalRounds: number;                           // 总回合数

  @Prop({ type: TeamStatistics, required: true }) 
  teamAStats: TeamStatistics;                    // A队统计

  @Prop({ type: TeamStatistics, required: true }) 
  teamBStats: TeamStatistics;                    // B队统计

  @Prop({ type: [GoalRecord], required: true }) 
  goals: GoalRecord[];                           // 进球记录列表

  @Prop({ type: BattleRewards }) 
  rewards?: BattleRewards;                        // 奖励信息

  @Prop({ type: [LootItem] }) 
  lootItems?: LootItem[];                         // 掉落物品列表

  @Prop({ type: [BattleRound], required: true }) 
  rounds: BattleRound[];                          // 战斗回合列表

  @Prop({ type: [SkillRecord], required: true })
  skillRecords: SkillRecord[];                    // 技能记录数组 [teamA, teamB]
}

/**
 * 战斗队伍实体
 * @description 对应原 preBattleInfo 中的单个队伍
 */
@Schema({ _id: false, versionKey: false })
export class TeamSnapshoot {
  @Prop({ required: true, index: true, maxlength: 50 })
  characterId: string;                           // 玩家角色ID

  @Prop({ type: String, enum: Object.values(TeamType), required: true })
  teamType: TeamType;                            // 队伍类型 (teamA/teamB)

  @Prop({ required: true, maxlength: 50 })
  teamName: string;                              // 队伍名称

  @Prop({ required: true, min: 0, max: 2000 })
  rating: number;                                // 队伍评分 (基于old项目数据范围，初始值可为0)

  @Prop({ required: true, min: 100000, max: 999999 })
  formationId: number;                           // 阵型ID (基于old项目442101格式)

  @Prop({ required: true, min: 100, max: 999 })
  attackTacticId: number;                        // 攻击战术ID (基于old项目101格式)

  @Prop({ required: true, min: 100, max: 999 })
  defendTacticId: number;                        // 防守战术ID (基于old项目202格式)

  @Prop({ type: [HeroSnapshoot], required: true, validate: [arrayLimit11, '{PATH} 必须是11名球员'] })
  heroes: HeroSnapshoot[];                       // 球员列表 (11人)

  @Prop({ min: 0, max: 10, default: 0 })
  attackTacticsState?: number;                   // 攻击战术状态

  @Prop({ min: 0, max: 10, default: 0 })
  defendTacticsState?: number;                   // 防守战术状态

  @Prop({ min: 0, max: 10000, default: 0 })
  baseAttackValue?: number;                      // 基础攻击值

  @Prop({ min: 0, max: 10000, default: 0 })
  baseDefendValue?: number;                      // 基础防守值

  @Prop({ min: 0.1, max: 5.0, default: 1.0 })
  attackValueFactor?: number;                    // 攻击加成因子

  @Prop({ min: 0.1, max: 5.0, default: 1.0 })
  defendValueFactor?: number;                    // 防守加成因子

  @Prop({ maxlength: 200 })
  teamAvatar?: string;                           // 队伍头像URL

  @Prop({ maxlength: 100 })
  battleName?: string;                           // 战斗名称

  @Prop({ min: 1, max: 20 })
  battleType?: number;                           // 战斗类型标识
}

/**
 * 战斗房间 - 主文档
 * @description 顶层数据容器, 包含双方队伍信息和战斗结果
 * @example 数据结构层级关系
 * BattleRoom (顶层容器)
 * ├── teamA: TeamSnapshoot
 * │   └── heroes: HeroSnapshoot[]
 * ├── teamB: TeamSnapshoot
 * │   └── heroes: HeroSnapshoot[]
 * └── result: BattleResult
 *     ├── teamAStats: TeamStatistics
 *     ├── teamBStats: TeamStatistics
 *     ├── goals: GoalRecord[]
 *     ├── rewards: BattleRewards
 *     ├── lootItems: LootItem[]
 *     ├── rounds: BattleRound[]
 *     │   └── periodInfo: PeriodInfo[]
 *     │       ├── A1Info: HeroActionInfo
 *     │       ├── A2Info: HeroActionInfo
 *     │       ├── BInfo: HeroActionInfo
 *     │       └── GKInfo: HeroActionInfo
 *     └── skillRecords: SkillRecord[]
 *         ├── durRecord: DurativeSkillRecord[]
 *         ├── insRecord: InstantSkillRecord[]
 *         └── nextAtkRecord: NextAttackSkillRecord[]
 */
@Schema({
  collection: 'battle_rooms',
  timestamps: true,
  versionKey: false,
})
export class BattleRoom extends Document {
  @Prop({ required: true, unique: true, maxlength: 50, index: true })
  roomId: string;                                  // 房间ID

  @Prop({ type: String, enum: Object.values(RoomStatus), required: true, index: true })
  status: RoomStatus;                              // 房间状态

  @Prop({ type: String, enum: Object.values(BattleType), required: true, index: true })
  battleType: BattleType;                          // 战斗类型

  // --- 队伍信息 ---
  @Prop({ type: TeamSnapshoot, required: true }) 
  teamA: TeamSnapshoot;                            // A队信息 (preBattleInfo[0])

  @Prop({ type: TeamSnapshoot, required: true }) 
  teamB: TeamSnapshoot;                            // B队信息 (preBattleInfo[1])

  // --- 战斗结果 ---
  @Prop({ type: BattleResult }) 
  result?: BattleResult;                           // 战斗结果和回放数据

  @Prop()
  finishedAt?: Date;                               // 战斗完成时间

  // --- 实时战斗状态 (用于战斗过程中的状态跟踪) ---
  @Prop({ default: 0 })
  scoreA?: number;                                 // A队当前比分

  @Prop({ default: 0 })
  scoreB?: number;                                 // B队当前比分

  @Prop({ default: 500 })
  moraleA?: number;                                // A队当前士气

  @Prop({ default: 500 })
  moraleB?: number;                                // B队当前士气
}

export const BattleRoomSchema = SchemaFactory.createForClass(BattleRoom);

// ==================== 索引创建 ====================
BattleRoomSchema.index({ roomId: 1 });
BattleRoomSchema.index({ status: 1 });
BattleRoomSchema.index({ battleType: 1 });
BattleRoomSchema.index({ createdAt: 1 });
BattleRoomSchema.index({ 'teamA.characterId': 1 });
BattleRoomSchema.index({ 'teamB.characterId': 1 });

// #############################################################################
//
// Methods & Document Type
//
// #############################################################################

/**
 * 战斗房间实例方法接口
 * @description 专注于房间状态管理和战斗回放记录获取
 */
export interface IBattleRoomMethods {
  // === 房间状态管理 ===
  isActive(): boolean;                                    // 检查房间是否激活
  isFinished(): boolean;                                  // 检查战斗是否完成
  isExpired(): boolean;                                   // 检查房间是否过期
  canStart(): boolean;                                    // 检查是否可以开始战斗

  // === 战斗生命周期管理 ===
  startBattle(): void;                                    // 开始战斗
  finishBattle(result: BattleResult): void;               // 完成战斗
  expireRoom(): void;                                     // 设置房间过期

  // === 战斗结果查询 ===
  getWinner(): number;                                    // 获取胜利方
  isDraw(): boolean;                                      // 检查是否平局
  getFinalScore(): { teamA: number; teamB: number };     // 获取最终比分
  getBattleDuration(): number;                            // 获取战斗持续时间

  // === 队伍信息管理 ===
  getTeamByCharacterId(characterId: string): TeamSnapshoot | null;  // 根据角色ID获取队伍
  getOpponentTeam(characterId: string): TeamSnapshoot | null;       // 获取对手队伍

  // === 实时状态更新 ===
  updateTeamScore(team: 'teamA' | 'teamB', score: number): void;    // 更新队伍比分
  updateTeamMorale(team: 'teamA' | 'teamB', morale: number): void;  // 更新队伍士气

  // === 战斗回放记录获取 ===
  getTotalRounds(): number;                               // 获取总回合数
  getBattleReplay(): any;                                 // 获取完整战斗回放数据
  getBattleReplaySummary(): any;                          // 获取战斗回放摘要
  getReplayByRounds(startRound: number, endRound: number): any;  // 获取指定回合范围的回放

  // === 数据验证 ===
  validateRoomData(): { isValid: boolean; errors: string[] };      // 验证房间数据完整性
  hasValidTeams(): boolean;                               // 检查是否有有效的队伍数据
}

export type BattleRoomDocument = BattleRoom & Document & IBattleRoomMethods;

// ==================== 实例方法实现 ====================

// === 房间状态管理 ===

/**
 * 检查房间是否激活
 */
BattleRoomSchema.methods.isActive = function(): boolean {
  return this.status === RoomStatus.ACTIVE;
};

/**
 * 检查战斗是否完成
 */
BattleRoomSchema.methods.isFinished = function(): boolean {
  return this.status === RoomStatus.FINISHED;
};

/**
 * 检查房间是否过期
 */
BattleRoomSchema.methods.isExpired = function(): boolean {
  return this.status === RoomStatus.EXPIRED;
};

/**
 * 检查是否可以开始战斗
 */
BattleRoomSchema.methods.canStart = function(): boolean {
  return (
    this.status === RoomStatus.ACTIVE &&
    this.hasValidTeams()
  );
};

// === 战斗生命周期管理 ===

/**
 * 开始战斗
 */
BattleRoomSchema.methods.startBattle = function(): void {
  if (this.canStart()) {
    this.status = RoomStatus.ACTIVE;
    // 初始化实时状态
    this.scoreA = 0;
    this.scoreB = 0;
    this.moraleA = 500;
    this.moraleB = 500;
  }
};

/**
 * 完成战斗
 */
BattleRoomSchema.methods.finishBattle = function(result: BattleResult): void {
  this.status = RoomStatus.FINISHED;
  this.result = result;
  this.finishedAt = new Date();
  // 更新最终比分
  if (result.rounds && result.rounds.length > 0) {
    const lastRound = result.rounds[result.rounds.length - 1];
    this.scoreA = lastRound.scoreA;
    this.scoreB = lastRound.scoreB;
  }
};

/**
 * 设置房间过期
 */
BattleRoomSchema.methods.expireRoom = function(): void {
  this.status = RoomStatus.EXPIRED;
  this.finishedAt = new Date();
};

// === 战斗结果查询 ===

/**
 * 获取胜利方
 */
BattleRoomSchema.methods.getWinner = function(): number {
  return this.result ? this.result.winner : 0;
};

/**
 * 检查是否平局
 */
BattleRoomSchema.methods.isDraw = function(): boolean {
  return this.getWinner() === 0;
};

/**
 * 获取最终比分
 */
BattleRoomSchema.methods.getFinalScore = function(): { teamA: number; teamB: number } {
  return {
    teamA: this.scoreA || 0,
    teamB: this.scoreB || 0
  };
};

/**
 * 获取战斗持续时间（毫秒）
 */
BattleRoomSchema.methods.getBattleDuration = function(): number {
  if (!this.finishedAt || !this.createdAt) return 0;
  return this.finishedAt.getTime() - this.createdAt.getTime();
};

// === 队伍信息管理 ===

/**
 * 根据角色ID获取队伍数据
 */
BattleRoomSchema.methods.getTeamByCharacterId = function(characterId: string): TeamSnapshoot | null {
  if (this.teamA?.characterId === characterId) return this.teamA;
  if (this.teamB?.characterId === characterId) return this.teamB;
  return null;
};

/**
 * 获取对手队伍
 */
BattleRoomSchema.methods.getOpponentTeam = function(characterId: string): TeamSnapshoot | null {
  if (this.teamA?.characterId === characterId) return this.teamB;
  if (this.teamB?.characterId === characterId) return this.teamA;
  return null;
};

// === 实时状态更新 ===

/**
 * 更新队伍比分
 */
BattleRoomSchema.methods.updateTeamScore = function(team: 'teamA' | 'teamB', score: number): void {
  if (team === 'teamA') this.scoreA = score;
  else this.scoreB = score;
};

/**
 * 更新队伍士气
 */
BattleRoomSchema.methods.updateTeamMorale = function(team: 'teamA' | 'teamB', morale: number): void {
  if (team === 'teamA') this.moraleA = morale;
  else this.moraleB = morale;
};

// === 战斗回放记录获取 ===

/**
 * 获取总回合数
 */
BattleRoomSchema.methods.getTotalRounds = function(): number {
  return this.result?.rounds?.length || 0;
};

/**
 * 获取完整战斗回放数据 (当前项目字段命名)
 */
BattleRoomSchema.methods.getBattleReplay = function(): any {
  return {
    roomId: this.roomId,
    battleType: this.battleType,
    status: this.status,
    teamSnapshots: [this.teamA, this.teamB],       // 队伍快照信息
    battleResult: this.result,                     // 完整战斗结果
    rounds: this.result?.rounds || [],             // 战斗回合数据
    statistics: this.result?.statistics || [],     // 统计数据
    goalRecords: this.result?.goalRecords || [],   // 进球记录
    lootItems: this.result?.lootItems || [],       // 奖励物品
    rewards: this.result?.rewards || {},           // 奖励信息
    skillRecords: this.result?.skillRecords || [], // 技能记录
    comments: this.result?.comments || [],         // 评论记录
    createdAt: this.createdAt,
    finishedAt: this.finishedAt,
    battleDuration: this.getBattleDuration()
  };
};

/**
 * 获取战斗回放摘要
 */
BattleRoomSchema.methods.getBattleReplaySummary = function(): any {
  return {
    roomId: this.roomId,
    battleType: this.battleType,
    status: this.status,
    winner: this.getWinner(),
    finalScore: this.getFinalScore(),
    totalRounds: this.getTotalRounds(),
    battleDuration: this.getBattleDuration(),
    teamA: {
      characterId: this.teamA?.characterId,
      teamName: this.teamA?.teamName,
      rating: this.teamA?.rating
    },
    teamB: {
      characterId: this.teamB?.characterId,
      teamName: this.teamB?.teamName,
      rating: this.teamB?.rating
    },
    createdAt: this.createdAt,
    finishedAt: this.finishedAt
  };
};

/**
 * 获取指定回合范围的回放
 */
BattleRoomSchema.methods.getReplayByRounds = function(startRound: number, endRound: number): any {
  const rounds = this.result?.rounds || [];
  const filteredRounds = rounds.slice(startRound, endRound + 1);

  return {
    roomId: this.roomId,
    roundRange: { start: startRound, end: endRound },
    rounds: filteredRounds,
    totalRounds: rounds.length
  };
};

// === 数据验证 ===

/**
 * 验证房间数据完整性
 */
BattleRoomSchema.methods.validateRoomData = function(): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // 验证基础字段
  if (!this.roomId) errors.push('房间ID不能为空');
  if (!this.battleType) errors.push('战斗类型不能为空');
  if (!this.status) errors.push('房间状态不能为空');

  // 验证队伍数据
  if (!this.teamA) errors.push('队伍A数据缺失');
  if (!this.teamB) errors.push('队伍B数据缺失');

  // 验证队伍完整性
  if (!this.hasValidTeams()) {
    errors.push('队伍数据不完整');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * 检查是否有有效的队伍数据
 */
BattleRoomSchema.methods.hasValidTeams = function(): boolean {
  return (
    this.teamA?.heroes?.length === 11 &&
    this.teamB?.heroes?.length === 11 &&
    this.teamA?.characterId &&
    this.teamB?.characterId
  );
};


