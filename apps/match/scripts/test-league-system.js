/**
 * 联赛系统测试模块
 * 
 * 测试内容：
 * 1. 获取联赛副本数据
 * 2. PVE联赛战斗
 * 3. 购买联赛次数
 * 4. 联赛奖励领取
 */

const chalk = require('chalk');

class LeagueSystemTester {
  constructor(socket, testData) {
    this.socket = socket;
    this.testData = testData;
    this.testResults = [];
  }

  /**
   * WebSocket调用封装 - 使用新的消息格式
   */
  async callWebSocket(command, data = {}) {
    return new Promise((resolve, reject) => {
      const messageId = `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      const message = {
        id: messageId,
        command: command,
        payload: {
          characterId: this.testData.characterId,
          serverId: this.testData.serverId || 'server_001',
          token: this.testData.token,
          ...data
        }
      };

      console.log(chalk.gray(`📤 发送消息: ${command}`));

      // 设置响应监听器
      const responseHandler = (response) => {
        if (response.id === messageId) {
          console.log(chalk.cyan(`📨 收到响应: ${command}`));
          this.socket.off('message', responseHandler);
          resolve(response);
        }
      };

      this.socket.on('message', responseHandler);

      // 发送消息
      this.socket.emit('message', message);

      // 设置超时
      setTimeout(() => {
        this.socket.off('message', responseHandler);
        reject(new Error(`消息超时: ${command}`));
      }, 10000);
    });
  }

  /**
   * 测试获取联赛副本数据
   */
  async testGetLeagueCopyData() {
    console.log(chalk.yellow('📋 测试获取联赛副本数据...'));

    try {
      const response = await this.callWebSocket('match.league.getLeagueCopyData');

      if (response?.payload?.success) {
        const data = response?.payload?.data;
        console.log(chalk.green('✅ 获取联赛副本数据成功'));
        console.log(chalk.gray(`   联赛数量: ${data.leagueCopyData?.length || 0}`));

        // 保存联赛数据供后续测试使用
        this.leagueData = data.leagueCopyData;

        if (data.leagueCopyData && data.leagueCopyData.length > 0) {
          const firstLeague = data.leagueCopyData[0];
          console.log(chalk.gray(`   第一个联赛ID: ${firstLeague.uid}`)); // 修复：使用uid而不是id
          console.log(chalk.gray(`   副本数量: ${firstLeague.copyData?.length || 0}`));
        }

        this.testResults.push({ test: 'getLeagueCopyData', success: true });
        return true;
      } else {
        console.log(chalk.red('❌ 获取联赛副本数据失败'));
        const errorMsg = response?.payload?.message || '未知错误';
        console.log(chalk.red(`   错误信息: ${errorMsg}`));
        this.testResults.push({ test: 'getLeagueCopyData', success: false, error: errorMsg });
        return false;
      }
    } catch (error) {
      console.log(chalk.red(`❌ 获取联赛副本数据异常: ${error.message}`));
      this.testResults.push({ test: 'getLeagueCopyData', success: false, error: error.message });
      return false;
    }
  }

  /**
   * 测试PVE联赛战斗
   */
  async testPveLeagueBattle() {
    console.log(chalk.yellow('⚔️ 测试PVE联赛战斗...'));
    
    try {
      // 检查是否有联赛数据
      if (!this.leagueData || this.leagueData.length === 0) {
        console.log(chalk.yellow('⚠️ 没有联赛数据，跳过PVE战斗测试'));
        return true;
      }

      const firstLeague = this.leagueData[0];
      if (!firstLeague.copyData || firstLeague.copyData.length === 0) {
        console.log(chalk.yellow('⚠️ 没有副本数据，跳过PVE战斗测试'));
        return true;
      }

      const firstCopy = firstLeague.copyData[0];

      const response = await this.callWebSocket('match.league.pveBattle', {
        leagueId: firstLeague.uid,  // 修复：使用uid而不是id
        teamCopyId: firstCopy.teamCopyId  // 修复：使用teamCopyId而不是teamId
      });

      if (response?.payload?.success) {
        const data = response?.payload?.data;
        console.log(chalk.green('✅ PVE联赛战斗成功'));

        // 修复：正确解析战斗结果数据结构
        const battleResult = data.battleResult || data;
        const winner = battleResult.winner;
        const homeScore = battleResult.homeScore || 0;
        const awayScore = battleResult.awayScore || 0;
        const roomId = battleResult.roomId;

        console.log(chalk.gray(`   战斗结果: ${winner === 1 ? '胜利' : winner === 0 ? '平局' : '失败'}`));
        console.log(chalk.gray(`   比分: ${homeScore}:${awayScore}`));

        // 获取并显示战斗回放 - 支持新的old项目格式
        if (roomId) {
          console.log(chalk.gray(`   房间ID: ${roomId}`));
          const replaySuccess = await this.getBattleReplayAndDisplay(roomId);

          // 如果战斗回放获取失败，但有战斗数据，则显示战斗记录
          if (!replaySuccess && battleResult) {
            console.log(chalk.yellow('📹 使用已有的战斗数据...'));
            this.displayEnhancedBattleRecord(battleResult);
          }
        } else if (battleResult) {
          // 直接显示战斗记录 - 使用新的增强格式
          console.log(chalk.yellow('📹 显示战斗回放数据...'));
          this.displayEnhancedBattleRecord(battleResult);
        } else {
          console.log(chalk.yellow('⚠️ 未找到战斗数据，无法显示战斗回放'));
        }

        this.testResults.push({ test: 'pveBattle', success: true });
        return true;
      } else {
        console.log(chalk.red('❌ PVE联赛战斗失败'));
        const errorMsg = response?.payload?.message || '未知错误';
        console.log(chalk.red(`   错误信息: ${errorMsg}`));
        this.testResults.push({ test: 'pveBattle', success: false, error: errorMsg });
        return false;
      }
    } catch (error) {
      console.log(chalk.red(`❌ PVE联赛战斗异常: ${error.message}`));
      this.testResults.push({ test: 'pveBattle', success: false, error: error.message });
      return false;
    }
  }

  /**
   * 测试购买联赛次数
   */
  async testBuyLeagueTimes() {
    console.log(chalk.yellow('💰 测试购买联赛次数...'));
    
    try {
      // 检查是否有联赛数据
      if (!this.leagueData || this.leagueData.length === 0) {
        console.log(chalk.yellow('⚠️ 没有联赛数据，跳过购买次数测试'));
        return true;
      }

      const firstLeague = this.leagueData[0];
      
      const response = await this.callWebSocket('match.league.buyLeagueTimes', {
        times: 1
      });

      const data = response?.payload?.data;

      if (response?.payload?.success) {
        console.log(chalk.green('✅ 购买联赛次数成功'));
        console.log(chalk.gray(`   购买数量: ${data.addedTimes || 1}`));
        console.log(chalk.gray(`   花费: ${data.totalCost || 0} 球币`));

        this.testResults.push({ test: 'buyLeagueTimes', success: true });
        return true;
      } else {
        console.log(chalk.red('❌ 购买联赛次数失败'));
        const errorMsg = response?.payload?.message || '未知错误';
        console.log(chalk.red(`   错误信息: ${errorMsg}`));
        this.testResults.push({ test: 'buyLeagueTimes', success: false, error: errorMsg });
        return false;
      }
    } catch (error) {
      console.log(chalk.red(`❌ 购买联赛次数异常: ${error.message}`));
      this.testResults.push({ test: 'buyLeagueTimes', success: false, error: error.message });
      return false;
    }
  }

  /**
   * 测试获取联赛统计信息
   */
  async testGetLeagueStatistics() {
    console.log(chalk.yellow('📊 测试获取联赛统计信息...'));
    
    try {
      const response = await this.callWebSocket('match.league.getStatistics');

      if (response?.payload?.success) {
        const data = response?.payload?.data;
        console.log(chalk.green('✅ 获取联赛统计信息成功'));
        console.log(chalk.gray(`   统计数据: ${JSON.stringify(data.data || {})}`));

        this.testResults.push({ test: 'getLeagueStatistics', success: true });
        return true;
      } else {
        console.log(chalk.red('❌ 获取联赛统计信息失败'));
        const errorMsg = response?.payload?.message || '未知错误';
        console.log(chalk.red(`   错误信息: ${errorMsg}`));
        this.testResults.push({ test: 'getLeagueStatistics', success: false, error: errorMsg });
        return false;
      }
    } catch (error) {
      console.log(chalk.red(`❌ 获取联赛统计信息异常: ${error.message}`));
      this.testResults.push({ test: 'getLeagueStatistics', success: false, error: error.message });
      return false;
    }
  }

  /**
   * 运行所有联赛系统测试
   */
  async runTests() {
    console.log(chalk.blue('\n=== 联赛系统测试 ==='));
    
    const tests = [
      () => this.testGetLeagueCopyData(),
      () => this.testPveLeagueBattle(),
      () => this.testBuyLeagueTimes(),
      () => this.testGetLeagueStatistics()
    ];

    let successCount = 0;
    
    for (const test of tests) {
      try {
        const result = await test();
        if (result) successCount++;
      } catch (error) {
        console.log(chalk.red(`测试执行异常: ${error.message}`));
      }
      
      // 测试间隔
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    console.log(chalk.blue(`\n联赛系统测试完成: ${successCount}/${tests.length} 通过`));
    
    // 显示详细结果
    this.testResults.forEach(result => {
      const status = result.success ? chalk.green('✅') : chalk.red('❌');
      const error = result.error ? chalk.gray(` (${result.error})`) : '';
      console.log(`${status} ${result.test}${error}`);
    });

    return successCount === tests.length;
  }

  /**
   * 获取战斗回放并可视化显示
   */
  async getBattleReplayAndDisplay(roomId) {
    try {
      console.log(chalk.yellow('📹 获取战斗回放数据...'));

      const response = await this.callWebSocket('match.battle.getBattleReplay', {
        roomId: roomId
      });

      if (response?.payload?.success) {
        const data = response?.payload?.data;
        console.log(chalk.green('✅ 战斗回放获取成功'));
        console.log(chalk.gray(`   回放数据: ${JSON.stringify(response, null, 2)}`));
        this.displayBattleReplay(data, roomId);
      } else {
        console.log(chalk.yellow(`⚠️ 战斗回放获取失败或无数据: ${response?.payload?.message || '未知错误'}`));
        console.log(chalk.gray(`   错误信息: ${'未知错误'}`));

        // 如果回放获取失败，但有战斗记录数据，则显示战斗记录
        console.log(chalk.yellow('📹 使用已有的战斗记录数据...'));
        return false; // 返回false，让调用方知道需要使用备用方案
      }
    } catch (error) {
      console.log(chalk.red(`❌ 获取战斗回放异常: ${error.message}`));
    }
  }

  /**
   * 可视化显示战斗回放
   */
  displayBattleReplay(replayData, roomId) {
    console.log(chalk.blue('\n' + '='.repeat(80)));
    console.log(chalk.blue(`🎬 战斗回放可视化显示 - 房间ID: ${roomId}`));
    console.log(chalk.blue('='.repeat(80)));

    // 验证固定的战斗回放数据格式
    if (!replayData || !replayData.battleRecord || !replayData.battleRecord.battleRoundInfo) {
      console.log(chalk.yellow('⚠️ 战斗回放数据格式错误或为空'));
      return;
    }

    const battleRounds = replayData.battleRecord.battleRoundInfo;
    const teamAData = replayData.teamAData;
    const teamBData = replayData.teamBData;

    // 显示战斗基本信息
    if (replayData.battleResult) {
      console.log(chalk.cyan(`⚔️  战斗信息:`));
      console.log(chalk.gray(`   战斗类型: ${replayData.battleResult.battleType}`));
      console.log(chalk.gray(`   最终比分: ${replayData.battleResult.homeScore}:${replayData.battleResult.awayScore}`));
      console.log(chalk.gray(`   获胜方: ${replayData.battleResult.winner === 1 ? '主队' : replayData.battleResult.winner === 2 ? '客队' : '平局'}`));
      console.log(chalk.gray(`   战斗时长: ${replayData.battleResult.totalTime}秒`));
    }

    // 显示回放统计信息
    console.log(chalk.cyan(`📊 回放统计:`));
    console.log(chalk.gray(`   总回合数: ${replayData.battleRecord.totalRounds || battleRounds.length}`));
    console.log(chalk.gray(`   总时间: ${replayData.battleRecord.totalTime || 0}秒`));
    console.log(chalk.gray(`   数据大小: ${JSON.stringify(replayData).length} 字符`));

    // 显示队伍信息
    if (teamAData) {
      console.log(chalk.green(`🏠 主队: ${teamAData.teamName} (角色: ${teamAData.characterId})`));
      console.log(chalk.gray(`   阵型: ${teamAData.formationId}, 球员数: ${teamAData.heroes?.length || 0}`));
    }
    if (teamBData) {
      console.log(chalk.red(`✈️  客队: ${teamBData.teamName} (角色: ${teamBData.characterId})`));
      console.log(chalk.gray(`   阵型: ${teamBData.formationId}, 球员数: ${teamBData.heroes?.length || 0}`));
    }

    // 显示队伍详细信息 - 使用优化后的TeamSnapshoot结构
    console.log(chalk.cyan(`🎯 队伍详细信息:`));
    if (teamAData) {
      console.log(chalk.gray(`   主队: ${teamAData.teamName} (评分: ${teamAData.rating})`));
      console.log(chalk.gray(`     阵型: ${teamAData.formationId}, 攻击战术: ${teamAData.attackTacticId}, 防守战术: ${teamAData.defendTacticId}`));
      if (teamAData.heroes && teamAData.heroes.length > 0) {
        console.log(chalk.gray(`     球员: ${teamAData.heroes.slice(0, 3).map(h => h.name).join(', ')}... (共${teamAData.heroes.length}人)`));
      }
    }
    if (teamBData) {
      console.log(chalk.gray(`   客队: ${teamBData.teamName} (评分: ${teamBData.rating})`));
      console.log(chalk.gray(`     阵型: ${teamBData.formationId}, 攻击战术: ${teamBData.attackTacticId}, 防守战术: ${teamBData.defendTacticId}`));
      if (teamBData.heroes && teamBData.heroes.length > 0) {
        console.log(chalk.gray(`     球员: ${teamBData.heroes.slice(0, 3).map(h => h.name).join(', ')}... (共${teamBData.heroes.length}人)`));
      }
    }

    // 显示进球记录 - 使用BattleResult中的goals
    if (replayData.battleResult && replayData.battleResult.goals && replayData.battleResult.goals.length > 0) {
      console.log(chalk.cyan(`⚽ 进球记录:`));
      replayData.battleResult.goals.forEach((goal, index) => {
        console.log(chalk.yellow(`   ${index + 1}. ${goal.time}'分钟 - ${goal.teamType === 'teamA' ? '主队' : '客队'} ${goal.ballerName} 进球`));
      });
    }

    // 显示技能记录 - 使用BattleResult中的skillRecords
    if (replayData.battleResult && replayData.battleResult.skillRecords && replayData.battleResult.skillRecords.length > 0) {
      console.log(chalk.cyan(`✨ 技能记录:`));
      replayData.battleResult.skillRecords.forEach((skillTeam, teamIndex) => {
        const teamType = teamIndex === 0 ? '主队' : '客队';
        if (skillTeam.durRecord && skillTeam.durRecord.length > 0) {
          console.log(chalk.gray(`   ${teamType}持续技能: ${skillTeam.durRecord.length}个`));
        }
        if (skillTeam.insRecord && skillTeam.insRecord.length > 0) {
          console.log(chalk.gray(`   ${teamType}瞬发技能: ${skillTeam.insRecord.length}个`));
        }
      });
    }

    // 显示队伍统计信息
    if (replayData.battleResult && (replayData.battleResult.teamAStats || replayData.battleResult.teamBStats)) {
      console.log(chalk.cyan(`📈 队伍统计:`));
      if (replayData.battleResult.teamAStats) {
        const stats = replayData.battleResult.teamAStats;
        console.log(chalk.gray(`   主队: 射门${stats.shotNum}次, 控球率${stats.ctrlBallPer}%, 突破率${stats.breakPer}%`));
        if (stats.bestBaller) {
          console.log(chalk.gray(`     最佳球员: ${stats.bestBaller}`));
        }
      }
      if (replayData.battleResult.teamBStats) {
        const stats = replayData.battleResult.teamBStats;
        console.log(chalk.gray(`   客队: 射门${stats.shotNum}次, 控球率${stats.ctrlBallPer}%, 突破率${stats.breakPer}%`));
        if (stats.bestBaller) {
          console.log(chalk.gray(`     最佳球员: ${stats.bestBaller}`));
        }
      }
    }

    // 逐回合显示战斗过程
    battleRounds.forEach((round, index) => {
      this.displayBattleRound(round, index + 1, teamAData, teamBData);
    });

    console.log(chalk.blue('\n' + '='.repeat(80)));
    console.log(chalk.blue('🏁 战斗回放显示完成'));
    console.log(chalk.blue('='.repeat(80) + '\n'));
  }

  /**
   * 显示单个战斗回合（适配新的数据结构）
   */
  displayBattleRound(round, roundNumber, teamAData, teamBData) {
    if (!round) return;

    console.log(chalk.yellow(`\n⚽ 第${roundNumber}回合 (${round.eventTime || 0}秒):`));

    // 显示回合基本信息
    const attackerType = round.attackerType || 'teamA';
    const attackMode = round.attackMode || 1;
    const scoreA = round.scoreA || 0;
    const scoreB = round.scoreB || 0;
    const moraleA = round.moraleA || 0;
    const moraleB = round.moraleB || 0;

    console.log(chalk.cyan(`   攻击方: ${attackerType === 'teamA' ? '主队' : '客队'} | 攻击模式: ${attackMode} | 比分: ${scoreA}:${scoreB}`));
    console.log(chalk.gray(`   士气: 主队${moraleA} vs 客队${moraleB}`));

    // 显示士气槽信息
    if (round.moraleSlotA !== undefined || round.moraleSlotB !== undefined) {
      console.log(chalk.gray(`   士气槽: 主队${round.moraleSlotA || 0} vs 客队${round.moraleSlotB || 0}`));
    }

    // 显示阶段信息
    if (round.periodInfo && Array.isArray(round.periodInfo)) {
      round.periodInfo.forEach((period, periodIndex) => {
        this.displayPeriodInfo(period, periodIndex + 1, teamAData, teamBData);
      });
    }

    // 显示评论信息
    if (round.comments && Array.isArray(round.comments) && round.comments.length > 0) {
      console.log(chalk.magenta(`   💬 评论: ${round.comments.length}条`));
      round.comments.forEach((comment, commentIndex) => {
        console.log(chalk.gray(`      ${commentIndex + 1}. ${comment.text || comment.content || JSON.stringify(comment)}`));
      });
    }
  }

  /**
   * 显示阶段信息
   */
  displayPeriodInfo(period, periodNumber, teamAData, teamBData) {
    if (!period) return;

    const periodNames = ['发起', '推进', '射门'];
    const periodName = periodNames[periodNumber - 1] || `阶段${periodNumber}`;
    const actionResult = period.actionResult;
    const actionPer = period.actionPer || 0;
    const successRate = (actionPer / 10).toFixed(1); // 转换为百分比

    // 根据结果显示不同颜色
    let resultColor = chalk.gray;
    let resultText = '未知';
    let resultIcon = '❓';

    if (actionResult === 1) {
      resultColor = chalk.green;
      resultText = '成功';
      resultIcon = '✅';
    } else if (actionResult === 0) {
      resultColor = chalk.red;
      resultText = '失败';
      resultIcon = '❌';
    } else if (actionResult === 2) {
      resultColor = chalk.yellow;
      resultText = '部分成功';
      resultIcon = '⚠️';
    }

    console.log(resultColor(`     ${resultIcon} ${periodName}阶段: ${resultText} (成功率: ${successRate}%)`));

    // 显示参与球员信息
    this.displayHeroActionInfo('主攻', period.A1Info, teamAData);
    this.displayHeroActionInfo('助攻', period.A2Info, teamAData);
    this.displayHeroActionInfo('防守', period.BInfo, teamBData);
    this.displayHeroActionInfo('门将', period.GKInfo, teamBData);

    // 显示技能效果
    if (period.skillEffectList && Array.isArray(period.skillEffectList) && period.skillEffectList.length > 0) {
      console.log(chalk.magenta(`       ✨ 技能效果: ${period.skillEffectList.length}个`));
      period.skillEffectList.forEach((skill, skillIndex) => {
        const teamType = skill.teamType === 'teamA' ? '主队' : '客队';
        const heroName = this.getHeroName(skill.heroId, teamAData, teamBData);
        console.log(chalk.gray(`         ${skillIndex + 1}. 技能${skill.skillId} (${teamType}${heroName}) +${skill.addPer}%`));
      });
    }
  }

  /**
   * 显示球员动作信息
   */
  displayHeroActionInfo(role, heroInfo, teamData) {
    if (!heroInfo || !heroInfo.heroId) return;

    const heroName = this.getHeroName(heroInfo.heroId, teamData);
    const attr1 = this.getAttributeName(heroInfo.attrType1);
    const attr2 = this.getAttributeName(heroInfo.attrType2);
    const value1 = heroInfo.attrValue1 + (heroInfo.addValue1 || 0);
    const value2 = heroInfo.attrValue2 + (heroInfo.addValue2 || 0);

    console.log(chalk.gray(`       👤 ${role}: ${heroName} (${attr1}:${value1}, ${attr2}:${value2})`));
  }

  /**
   * 获取球员姓名
   */
  getHeroName(heroId, teamAData, teamBData) {
    // 先从teamAData查找
    if (teamAData && teamAData.heroes) {
      const hero = teamAData.heroes.find(h => h.heroId === heroId);
      if (hero) return hero.name || heroId;
    }

    // 再从teamBData查找
    if (teamBData && teamBData.heroes) {
      const hero = teamBData.heroes.find(h => h.heroId === heroId);
      if (hero) return hero.name || heroId;
    }

    return heroId;
  }

  /**
   * 获取属性名称
   */
  getAttributeName(attrType) {
    const attrNames = {
      1: '攻击',
      2: '防守',
      3: '速度',
      4: '力量',
      5: '技术',
      6: '体能',
      7: '门将'
    };
    return attrNames[attrType] || `属性${attrType}`;
  }



  /**
   * 显示充满游戏性的战斗回放过程（适配新的战斗系统数据结构）
   */
  displayEnhancedBattleRecord(battleResult) {
    console.log(chalk.blue('\n' + '='.repeat(80)));
    console.log(chalk.blue(`足球经理战斗回放 - 房间ID: ${battleResult.roomId}`));
    console.log(chalk.blue('='.repeat(80)));

    // 显示比赛开场信息
    this.displayMatchIntro(battleResult);

    // 显示实时战斗过程
    this.displayLiveBattleProcess(battleResult);

    // 显示比赛结束总结
    this.displayMatchSummary(battleResult);

    console.log(chalk.blue('\n' + '='.repeat(80)));
    console.log(chalk.blue('🏁 比赛结束！感谢观看本场精彩对决！'));
    console.log(chalk.blue('='.repeat(80) + '\n'));
  }

  /**
   * 显示比赛开场信息
   */
  displayMatchIntro(battleResult) {
    console.log(chalk.yellow('\n🎺 比赛即将开始！'));

    // 获取队伍信息
    const teamAName = battleResult.teamA?.teamName || battleResult.preBattleInfo?.[0]?.teamName || '主队';
    const teamBName = battleResult.teamB?.teamName || battleResult.preBattleInfo?.[1]?.teamName || '客队';
    const teamARating = battleResult.teamA?.rating || battleResult.preBattleInfo?.[0]?.rating || 0;
    const teamBRating = battleResult.teamB?.rating || battleResult.preBattleInfo?.[1]?.rating || 0;

    console.log(chalk.green(`🏠 主队: ${teamAName} (评分: ${teamARating})`));
    console.log(chalk.red(`✈️  客队: ${teamBName} (评分: ${teamBRating})`));

    // 显示阵型信息
    if (battleResult.preBattleInfo && battleResult.preBattleInfo.length >= 2) {
      const teamAFormation = battleResult.preBattleInfo[0].formationID || 0;
      const teamBFormation = battleResult.preBattleInfo[1].formationID || 0;
      console.log(chalk.cyan(`📐 阵型对比: ${teamAName} (${teamAFormation}) vs ${teamBName} (${teamBFormation})`));
    }

    // 显示实力对比
    if (battleResult.teamA && battleResult.teamB) {
      const teamATotal = (battleResult.teamA.totalAttack || 0) + (battleResult.teamA.totalDefend || 0);
      const teamBTotal = (battleResult.teamB.totalAttack || 0) + (battleResult.teamB.totalDefend || 0);
      console.log(chalk.magenta(`⚡ 实力对比: ${teamAName} (${teamATotal}) vs ${teamBName} (${teamBTotal})`));
    }

    console.log(chalk.yellow('🔥 裁判吹响开场哨！比赛正式开始！'));
    console.log(chalk.gray('─'.repeat(60)));
  }

  /**
   * 显示实时战斗过程
   */
  displayLiveBattleProcess(battleResult) {
    console.log(chalk.yellow('\n⚽ 比赛进行中...'));

    // 获取战斗回合数据 - 使用固定的新格式
    if (!battleResult.battleRecord || !battleResult.battleRecord.battleRoundInfo) {
      console.log(chalk.yellow('⚠️ 没有找到战斗回合数据'));
      return;
    }

    const battleRounds = battleResult.battleRecord.battleRoundInfo;

    // 实时播放战斗过程
    let currentScore = { teamA: 0, teamB: 0 };

    battleRounds.forEach((round, index) => {
      const eventTime = round.eventTime || (index * 2 + 1);

      // 显示攻击回合
      this.displayAttackRound(round, eventTime, currentScore, index + 1);

      // 更新比分（从回合数据中获取最新比分）
      currentScore.teamA = round.scoreA || currentScore.teamA;
      currentScore.teamB = round.scoreB || currentScore.teamB;
    });
  }

  /**
   * 显示单个攻击回合
   */
  displayAttackRound(round, time, currentScore, roundNumber) {
    const isTeamA = round.attackerType === 'teamA';
    const teamName = isTeamA ? '主队' : '客队';
    const teamColor = isTeamA ? chalk.green : chalk.red;

    // 检查是否有进球（从阶段信息中判断）
    const isGoal = round.periodInfo && round.periodInfo.some(p => p.actionResult === 1 && p.GKInfo);

    // 显示时间和回合
    console.log(chalk.cyan(`\n⏱️  第${time}秒 - 第${roundNumber}回合`));

    // 显示攻击过程 - 使用新的periodInfo格式
    if (round.periodInfo && Array.isArray(round.periodInfo)) {
      console.log(teamColor(`🏃 ${teamName}发起进攻！`));

      const phases = ['发起', '推进', '射门'];
      round.periodInfo.forEach((period, index) => {
        const phaseIcon = ['🚀', '⚡', '🎯'][index];
        const success = period.actionResult === 1 ? '成功' : '失败';
        const successColor = period.actionResult === 1 ? chalk.green : chalk.red;
        const successRate = ((period.actionPer || 0) / 10).toFixed(1);

        console.log(chalk.gray(`   ${phaseIcon} ${phases[index]}阶段: ${successColor(success)} (成功率: ${successRate}%)`));

        // 显示参与球员
        if (period.A1Info && period.A1Info.heroId) {
          console.log(chalk.gray(`      主攻球员: ${period.A1Info.heroId}`));
        }
        if (period.A2Info && period.A2Info.heroId) {
          console.log(chalk.gray(`      助攻球员: ${period.A2Info.heroId}`));
        }
        if (period.BInfo && period.BInfo.heroId) {
          console.log(chalk.gray(`      防守球员: ${period.BInfo.heroId}`));
        }
        if (period.GKInfo && period.GKInfo.heroId) {
          console.log(chalk.gray(`      门将: ${period.GKInfo.heroId}`));
        }
      });
    }

    // 显示结果
    if (isGoal) {
      console.log(chalk.yellow(`🎉 ⚽ 进球！！！ ${teamName}破门得分！`));
      console.log(chalk.yellow(`🏆 比分更新: ${round.scoreA || 0} - ${round.scoreB || 0}`));
    } else {
      const defenderTeam = isTeamA ? '客队' : '主队';
      console.log(chalk.blue(`🛡️  ${defenderTeam}成功防守！`));
    }

    // 显示技能效果
    const allSkillEffects = round.periodInfo ?
      round.periodInfo.flatMap(p => p.skillEffectList || []) : [];

    if (allSkillEffects.length > 0) {
      console.log(chalk.magenta(`✨ 技能发动: ${allSkillEffects.length}个技能效果`));
      allSkillEffects.forEach(skill => {
        const teamType = skill.teamType === 'teamA' ? '主队' : '客队';
        console.log(chalk.gray(`   - 技能${skill.skillId} (${teamType}) +${skill.addPer}%`));
      });
    }

    console.log(chalk.gray('─'.repeat(50)));
  }

  /**
   * 显示比赛结束总结
   */
  displayMatchSummary(battleResult) {
    console.log(chalk.yellow('\n🏁 全场比赛结束！'));

    // 显示最终比分
    const finalScoreA = battleResult.homeScore || battleResult.teamAScore || 0;
    const finalScoreB = battleResult.awayScore || battleResult.teamBScore || 0;
    const teamAName = battleResult.teamA?.teamName || battleResult.preBattleInfo?.[0]?.teamName || '主队';
    const teamBName = battleResult.teamB?.teamName || battleResult.preBattleInfo?.[1]?.teamName || '客队';

    console.log(chalk.cyan(`📊 最终比分: ${teamAName} ${finalScoreA} - ${finalScoreB} ${teamBName}`));

    // 显示比赛结果
    let resultText = '';
    let resultColor = chalk.yellow;
    if (battleResult.winner === 1) {
      resultText = `🏆 ${teamAName} 获得胜利！`;
      resultColor = chalk.green;
    } else if (battleResult.winner === 2) {
      resultText = `🏆 ${teamBName} 获得胜利！`;
      resultColor = chalk.red;
    } else {
      resultText = '🤝 双方战成平局！';
      resultColor = chalk.yellow;
    }
    console.log(resultColor(resultText));

    // 显示比赛统计
    if (battleResult.battleEndInfo && battleResult.battleEndInfo.stInfos) {
      const stInfos = battleResult.battleEndInfo.stInfos;
      if (stInfos.length >= 2) {
        console.log(chalk.cyan('\n📈 比赛统计:'));
        console.log(chalk.gray(`   ${teamAName}: 射门 ${stInfos[0].shotNum || 0} 次`));
        console.log(chalk.gray(`   ${teamBName}: 射门 ${stInfos[1].shotNum || 0} 次`));
      }
    }

    // 显示奖励信息
    if (battleResult.battleEndInfo) {
      const endInfo = battleResult.battleEndInfo;

      if (endInfo.lootItemList && endInfo.lootItemList.length > 0) {
        console.log(chalk.yellow('\n💰 获得奖励:'));
        endInfo.lootItemList.forEach((item, index) => {
          console.log(chalk.gray(`   ${index + 1}. 物品ID: ${item.resId}, 数量: ${item.num}`));
        });
      }

      if (endInfo.rewards) {
        console.log(chalk.yellow('\n💰 额外奖励:'));
        if (endInfo.rewards.experience) console.log(chalk.gray(`   经验: +${endInfo.rewards.experience}`));
        if (endInfo.rewards.coins) console.log(chalk.gray(`   金币: +${endInfo.rewards.coins}`));
        if (endInfo.rewards.reputation) console.log(chalk.gray(`   声望: +${endInfo.rewards.reputation}`));
      }
    }
  }


}

module.exports = LeagueSystemTester;
